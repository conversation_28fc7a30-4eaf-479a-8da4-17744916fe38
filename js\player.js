import Card from './card.js';
import { CardPattern } from './deck.js';

/**
 * 玩家类 - 表示游戏中的一个玩家
 */
export default class Player {
    // 玩家类型常量
    static TYPES = {
        HUMAN: 'human',
        AI: 'ai'
    };

    // 玩家角色常量
    static ROLES = {
        LANDLORD: 'landlord',  // 地主
        FARMER: 'farmer'       // 农民
    };

    /**
     * 构造函数
     * @param {string} id 玩家ID
     * @param {string} name 玩家名称
     * @param {string} type 玩家类型
     */
    constructor(id, name, type = Player.TYPES.HUMAN) {
        this.id = id;
        this.name = name;
        this.type = type;
        this.role = null;
        this.hand = [];
        this.score = 0;
        this.totalScore = 0;
        this.isActive = false;
        this.hasCalledLandlord = false;
        this.lastPlayedCards = [];
        this.position = { x: 0, y: 0 };
        this.statistics = {
            gamesPlayed: 0,
            gamesWon: 0,
            landlordWins: 0,
            farmerWins: 0,
            bombsPlayed: 0,
            rocketsPlayed: 0
        };
    }

    /**
     * 添加卡牌到手牌
     * @param {Card|Card[]} cards 要添加的卡牌
     */
    addCards(cards) {
        if (Array.isArray(cards)) {
            this.hand.push(...cards);
        } else {
            this.hand.push(cards);
        }
        this.sortHand();
    }

    /**
     * 移除手牌中的指定卡牌
     * @param {Card[]} cards 要移除的卡牌
     * @returns {boolean} 是否成功移除
     */
    removeCards(cards) {
        const cardsToRemove = Array.isArray(cards) ? cards : [cards];
        
        for (const cardToRemove of cardsToRemove) {
            const index = this.hand.findIndex(card => card.equals(cardToRemove));
            if (index === -1) {
                return false; // 找不到要移除的卡牌
            }
            this.hand.splice(index, 1);
        }
        
        this.lastPlayedCards = [...cardsToRemove];
        return true;
    }

    /**
     * 获取手牌数量
     * @returns {number} 手牌数量
     */
    getHandCount() {
        return this.hand.length;
    }

    /**
     * 检查是否有指定的卡牌
     * @param {Card[]} cards 要检查的卡牌
     * @returns {boolean} 是否拥有这些卡牌
     */
    hasCards(cards) {
        const cardsToCheck = Array.isArray(cards) ? cards : [cards];
        
        for (const cardToCheck of cardsToCheck) {
            if (!this.hand.some(card => card.equals(cardToCheck))) {
                return false;
            }
        }
        return true;
    }

    /**
     * 对手牌进行排序
     */
    sortHand() {
        this.hand.sort((a, b) => {
            // 先按权重排序，再按花色排序
            const weightDiff = a.compareTo(b);
            if (weightDiff !== 0) return weightDiff;
            
            // 权重相同时按花色排序
            if (a.suit && b.suit) {
                const suitOrder = ['clubs', 'diamonds', 'hearts', 'spades'];
                return suitOrder.indexOf(a.suit) - suitOrder.indexOf(b.suit);
            }
            return 0;
        });
    }

    /**
     * 清空手牌
     */
    clearHand() {
        this.hand = [];
        this.lastPlayedCards = [];
    }

    /**
     * 设置玩家角色
     * @param {string} role 角色
     */
    setRole(role) {
        this.role = role;
    }

    /**
     * 判断是否为地主
     * @returns {boolean} 是否为地主
     */
    isLandlord() {
        return this.role === Player.ROLES.LANDLORD;
    }

    /**
     * 判断是否为农民
     * @returns {boolean} 是否为农民
     */
    isFarmer() {
        return this.role === Player.ROLES.FARMER;
    }

    /**
     * 判断是否为AI玩家
     * @returns {boolean} 是否为AI玩家
     */
    isAI() {
        return this.type === Player.TYPES.AI;
    }

    /**
     * 判断是否为人类玩家
     * @returns {boolean} 是否为人类玩家
     */
    isHuman() {
        return this.type === Player.TYPES.HUMAN;
    }

    /**
     * 设置活跃状态
     * @param {boolean} active 是否活跃
     */
    setActive(active) {
        this.isActive = active;
    }

    /**
     * 叫地主
     * @param {boolean} call 是否叫地主
     */
    callLandlord(call = true) {
        this.hasCalledLandlord = call;
    }

    /**
     * 重置叫地主状态
     */
    resetLandlordCall() {
        this.hasCalledLandlord = false;
    }

    /**
     * 增加分数
     * @param {number} points 分数
     */
    addScore(points) {
        this.score += points;
        this.totalScore += points;
    }

    /**
     * 重置当前游戏分数
     */
    resetScore() {
        this.score = 0;
    }

    /**
     * 设置位置
     * @param {number} x X坐标
     * @param {number} y Y坐标
     */
    setPosition(x, y) {
        this.position.x = x;
        this.position.y = y;
    }

    /**
     * 获取可以出的牌型
     * @param {Object|null} lastPattern 上一个牌型
     * @returns {Object[]} 可出的牌型数组
     */
    getPlayablePatterns(lastPattern = null) {
        const playablePatterns = [];
        
        // 如果没有上一个牌型，可以出任意牌型
        if (!lastPattern) {
            return this.getAllPossiblePatterns();
        }

        // 寻找能够压过上一个牌型的牌型
        const allPatterns = this.getAllPossiblePatterns();
        
        for (const pattern of allPatterns) {
            if (CardPattern.compare(pattern, lastPattern) > 0) {
                playablePatterns.push(pattern);
            }
        }

        return playablePatterns;
    }

    /**
     * 获取所有可能的牌型
     * @returns {Object[]} 所有可能的牌型数组
     */
    getAllPossiblePatterns() {
        const patterns = [];
        const handCopy = [...this.hand];

        // 生成所有可能的卡牌组合
        for (let i = 1; i <= handCopy.length; i++) {
            const combinations = this.getCombinations(handCopy, i);
            
            for (const combination of combinations) {
                const pattern = CardPattern.recognize(combination);
                if (pattern) {
                    patterns.push(pattern);
                }
            }
        }

        // 去重并按权重排序
        const uniquePatterns = this.removeDuplicatePatterns(patterns);
        return uniquePatterns.sort((a, b) => a.weight - b.weight);
    }

    /**
     * 获取指定长度的所有组合
     * @param {Card[]} cards 卡牌数组
     * @param {number} length 组合长度
     * @returns {Card[][]} 组合数组
     */
    getCombinations(cards, length) {
        if (length === 1) {
            return cards.map(card => [card]);
        }

        const combinations = [];
        for (let i = 0; i <= cards.length - length; i++) {
            const first = cards[i];
            const rest = cards.slice(i + 1);
            const restCombinations = this.getCombinations(rest, length - 1);
            
            for (const combination of restCombinations) {
                combinations.push([first, ...combination]);
            }
        }

        return combinations;
    }

    /**
     * 移除重复的牌型
     * @param {Object[]} patterns 牌型数组
     * @returns {Object[]} 去重后的牌型数组
     */
    removeDuplicatePatterns(patterns) {
        const unique = [];
        const seen = new Set();

        for (const pattern of patterns) {
            const key = `${pattern.type}_${pattern.weight}_${pattern.cards.length}`;
            if (!seen.has(key)) {
                seen.add(key);
                unique.push(pattern);
            }
        }

        return unique;
    }

    /**
     * 更新统计信息
     * @param {string} event 事件类型
     * @param {*} data 事件数据
     */
    updateStatistics(event, data = null) {
        switch (event) {
            case 'gameStart':
                this.statistics.gamesPlayed++;
                break;
            case 'gameWin':
                this.statistics.gamesWon++;
                if (this.isLandlord()) {
                    this.statistics.landlordWins++;
                } else {
                    this.statistics.farmerWins++;
                }
                break;
            case 'bombPlayed':
                this.statistics.bombsPlayed++;
                break;
            case 'rocketPlayed':
                this.statistics.rocketsPlayed++;
                break;
        }
    }

    /**
     * 获取胜率
     * @returns {number} 胜率百分比
     */
    getWinRate() {
        if (this.statistics.gamesPlayed === 0) return 0;
        return Math.round((this.statistics.gamesWon / this.statistics.gamesPlayed) * 100);
    }

    /**
     * 转换为JSON对象
     * @returns {Object} JSON对象
     */
    toJSON() {
        return {
            id: this.id,
            name: this.name,
            type: this.type,
            role: this.role,
            hand: this.hand.map(card => card.toJSON()),
            score: this.score,
            totalScore: this.totalScore,
            isActive: this.isActive,
            hasCalledLandlord: this.hasCalledLandlord,
            lastPlayedCards: this.lastPlayedCards.map(card => card.toJSON()),
            position: this.position,
            statistics: this.statistics
        };
    }

    /**
     * 从JSON对象创建玩家
     * @param {Object} json JSON对象
     * @returns {Player} 玩家实例
     */
    static fromJSON(json) {
        const player = new Player(json.id, json.name, json.type);
        player.role = json.role;
        player.hand = json.hand.map(cardJson => Card.fromJSON(cardJson));
        player.score = json.score || 0;
        player.totalScore = json.totalScore || 0;
        player.isActive = json.isActive || false;
        player.hasCalledLandlord = json.hasCalledLandlord || false;
        player.lastPlayedCards = (json.lastPlayedCards || []).map(cardJson => Card.fromJSON(cardJson));
        player.position = json.position || { x: 0, y: 0 };
        player.statistics = json.statistics || {
            gamesPlayed: 0,
            gamesWon: 0,
            landlordWins: 0,
            farmerWins: 0,
            bombsPlayed: 0,
            rocketsPlayed: 0
        };
        return player;
    }

    /**
     * 转换为字符串表示
     * @returns {string} 字符串表示
     */
    toString() {
        return `Player(${this.name}, ${this.type}, ${this.hand.length} cards, ${this.score} points)`;
    }
}
