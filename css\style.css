/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
}

#gameContainer {
    width: 100%;
    max-width: 1400px;
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
    border: 1px solid rgba(255, 255, 255, 0.18);
    padding: 20px;
}

/* 游戏标题 */
#gameTitle {
    text-align: center;
    margin-bottom: 20px;
}

#gameTitle h1 {
    color: #fff;
    font-size: 2.5em;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    font-weight: bold;
}

/* 游戏主界面 */
#gameBoard {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;
}

#gameCanvas {
    background: linear-gradient(45deg, #2d5a27 0%, #4a7c59 100%);
    border-radius: 15px;
    box-shadow: inset 0 0 50px rgba(0, 0, 0, 0.3);
    border: 3px solid #8B4513;
}

/* 控制面板 */
#controlPanel {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    padding: 20px;
    backdrop-filter: blur(5px);
}

#gameButtons {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.game-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.game-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    background: linear-gradient(45deg, #ee5a24, #ff6b6b);
}

.game-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.game-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* 游戏信息面板 */
#gameInfo {
    display: flex;
    flex-direction: column;
    gap: 15px;
    color: white;
    min-width: 200px;
}

#scoreBoard {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    padding: 15px;
}

.player-score {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 16px;
    font-weight: bold;
}

#gameStatus {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    padding: 15px;
    text-align: center;
}

#statusText {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 5px;
}

#currentPlayer {
    font-size: 14px;
    opacity: 0.8;
}

/* 设置面板 */
#settingsPanel {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    min-width: 300px;
}

#settingsPanel h3 {
    text-align: center;
    margin-bottom: 20px;
    color: #333;
    font-size: 24px;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 8px;
}

.setting-item label {
    font-weight: bold;
    color: #333;
}

.setting-item input,
.setting-item select {
    padding: 5px 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    background: white;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    #gameCanvas {
        width: 100%;
        height: auto;
        max-width: 1000px;
        max-height: 600px;
    }
}

@media (max-width: 768px) {
    #gameContainer {
        padding: 10px;
    }
    
    #controlPanel {
        flex-direction: column;
        gap: 15px;
    }
    
    #gameButtons {
        justify-content: center;
    }
    
    .game-btn {
        padding: 10px 20px;
        font-size: 14px;
    }
    
    #gameTitle h1 {
        font-size: 2em;
    }
}

/* 卡牌相关样式 */
.card {
    position: absolute;
    width: 60px;
    height: 84px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
    border: 2px solid #333;
}

.card:hover {
    transform: translateY(-10px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

.card.selected {
    transform: translateY(-15px);
    box-shadow: 0 6px 16px rgba(255, 215, 0, 0.6);
    border-color: #FFD700;
}

.card-back {
    background: linear-gradient(45deg, #1e3c72, #2a5298);
    background-image: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 10px,
        rgba(255, 255, 255, 0.1) 10px,
        rgba(255, 255, 255, 0.1) 20px
    );
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}
