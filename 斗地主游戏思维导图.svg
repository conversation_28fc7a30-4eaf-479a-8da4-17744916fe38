<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .main-node { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #34495e; }
      .sub-node { font-family: Arial, sans-serif; font-size: 14px; fill: #7f8c8d; }
      .detail-node { font-family: Arial, sans-serif; font-size: 12px; fill: #95a5a6; }
      .main-rect { fill: #3498db; stroke: #2980b9; stroke-width: 2; rx: 10; ry: 10; }
      .sub-rect { fill: #e74c3c; stroke: #c0392b; stroke-width: 2; rx: 8; ry: 8; }
      .detail-rect { fill: #f39c12; stroke: #e67e22; stroke-width: 1; rx: 5; ry: 5; }
      .line { stroke: #34495e; stroke-width: 2; }
    </style>
  </defs>
  
  <!-- 标题 -->
  <text x="600" y="30" text-anchor="middle" class="title">HTML5斗地主游戏开发思维导图</text>
  
  <!-- 中心节点 -->
  <rect x="500" y="60" width="200" height="60" class="main-rect"/>
  <text x="600" y="95" text-anchor="middle" class="main-node">斗地主游戏</text>
  
  <!-- 主要分支 -->
  
  <!-- 1. 游戏核心逻辑 -->
  <line x1="500" y1="90" x2="200" y2="200" class="line"/>
  <rect x="100" y="180" width="200" height="40" class="sub-rect"/>
  <text x="200" y="205" text-anchor="middle" class="main-node">游戏核心逻辑</text>
  
  <!-- 游戏核心逻辑子节点 -->
  <line x1="200" y1="220" x2="80" y2="280" class="line"/>
  <rect x="20" y="265" width="120" height="30" class="detail-rect"/>
  <text x="80" y="285" text-anchor="middle" class="sub-node">发牌算法</text>
  
  <line x1="200" y1="220" x2="200" y2="280" class="line"/>
  <rect x="140" y="265" width="120" height="30" class="detail-rect"/>
  <text x="200" y="285" text-anchor="middle" class="sub-node">出牌规则</text>
  
  <line x1="200" y1="220" x2="320" y2="280" class="line"/>
  <rect x="260" y="265" width="120" height="30" class="detail-rect"/>
  <text x="320" y="285" text-anchor="middle" class="sub-node">胜负判断</text>
  
  <line x1="200" y1="220" x2="80" y2="340" class="line"/>
  <rect x="20" y="325" width="120" height="30" class="detail-rect"/>
  <text x="80" y="345" text-anchor="middle" class="sub-node">叫地主逻辑</text>
  
  <line x1="200" y1="220" x2="320" y2="340" class="line"/>
  <rect x="260" y="325" width="120" height="30" class="detail-rect"/>
  <text x="320" y="345" text-anchor="middle" class="sub-node">计分系统</text>
  
  <!-- 2. 用户界面 -->
  <line x1="550" y1="120" x2="200" y2="450" class="line"/>
  <rect x="100" y="430" width="200" height="40" class="sub-rect"/>
  <text x="200" y="455" text-anchor="middle" class="main-node">用户界面</text>
  
  <!-- 用户界面子节点 -->
  <line x1="200" y1="470" x2="80" y2="530" class="line"/>
  <rect x="20" y="515" width="120" height="30" class="detail-rect"/>
  <text x="80" y="535" text-anchor="middle" class="sub-node">游戏桌面</text>
  
  <line x1="200" y1="470" x2="200" y2="530" class="line"/>
  <rect x="140" y="515" width="120" height="30" class="detail-rect"/>
  <text x="200" y="535" text-anchor="middle" class="sub-node">卡牌显示</text>
  
  <line x1="200" y1="470" x2="320" y2="530" class="line"/>
  <rect x="260" y="515" width="120" height="30" class="detail-rect"/>
  <text x="320" y="535" text-anchor="middle" class="sub-node">按钮控制</text>
  
  <line x1="200" y1="470" x2="80" y2="590" class="line"/>
  <rect x="20" y="575" width="120" height="30" class="detail-rect"/>
  <text x="80" y="595" text-anchor="middle" class="sub-node">动画效果</text>
  
  <line x1="200" y1="470" x2="320" y2="590" class="line"/>
  <rect x="260" y="575" width="120" height="30" class="detail-rect"/>
  <text x="320" y="595" text-anchor="middle" class="sub-node">音效系统</text>
  
  <!-- 3. 卡牌系统 -->
  <line x1="650" y1="120" x2="1000" y2="200" class="line"/>
  <rect x="900" y="180" width="200" height="40" class="sub-rect"/>
  <text x="1000" y="205" text-anchor="middle" class="main-node">卡牌系统</text>
  
  <!-- 卡牌系统子节点 -->
  <line x1="1000" y1="220" x2="880" y2="280" class="line"/>
  <rect x="820" y="265" width="120" height="30" class="detail-rect"/>
  <text x="880" y="285" text-anchor="middle" class="sub-node">卡牌数据</text>
  
  <line x1="1000" y1="220" x2="1000" y2="280" class="line"/>
  <rect x="940" y="265" width="120" height="30" class="detail-rect"/>
  <text x="1000" y="285" text-anchor="middle" class="sub-node">卡牌排序</text>
  
  <line x1="1000" y1="220" x2="1120" y2="280" class="line"/>
  <rect x="1060" y="265" width="120" height="30" class="detail-rect"/>
  <text x="1120" y="285" text-anchor="middle" class="sub-node">牌型识别</text>
  
  <line x1="1000" y1="220" x2="880" y2="340" class="line"/>
  <rect x="820" y="325" width="120" height="30" class="detail-rect"/>
  <text x="880" y="345" text-anchor="middle" class="sub-node">洗牌算法</text>
  
  <line x1="1000" y1="220" x2="1120" y2="340" class="line"/>
  <rect x="1060" y="325" width="120" height="30" class="detail-rect"/>
  <text x="1120" y="345" text-anchor="middle" class="sub-node">卡牌比较</text>
  
  <!-- 4. AI对手 -->
  <line x1="650" y1="120" x2="1000" y2="450" class="line"/>
  <rect x="900" y="430" width="200" height="40" class="sub-rect"/>
  <text x="1000" y="455" text-anchor="middle" class="main-node">AI对手</text>
  
  <!-- AI对手子节点 -->
  <line x1="1000" y1="470" x2="880" y2="530" class="line"/>
  <rect x="820" y="515" width="120" height="30" class="detail-rect"/>
  <text x="880" y="535" text-anchor="middle" class="sub-node">决策算法</text>
  
  <line x1="1000" y1="470" x2="1000" y2="530" class="line"/>
  <rect x="940" y="515" width="120" height="30" class="detail-rect"/>
  <text x="1000" y="535" text-anchor="middle" class="sub-node">难度等级</text>
  
  <line x1="1000" y1="470" x2="1120" y2="530" class="line"/>
  <rect x="1060" y="515" width="120" height="30" class="detail-rect"/>
  <text x="1120" y="535" text-anchor="middle" class="sub-node">策略模式</text>
  
  <line x1="1000" y1="470" x2="880" y2="590" class="line"/>
  <rect x="820" y="575" width="120" height="30" class="detail-rect"/>
  <text x="880" y="595" text-anchor="middle" class="sub-node">记忆系统</text>
  
  <line x1="1000" y1="470" x2="1120" y2="590" class="line"/>
  <rect x="1060" y="575" width="120" height="30" class="detail-rect"/>
  <text x="1120" y="595" text-anchor="middle" class="sub-node">反应时间</text>
  
  <!-- 5. 技术实现 -->
  <line x1="600" y1="120" x2="600" y2="650" class="line"/>
  <rect x="500" y="630" width="200" height="40" class="sub-rect"/>
  <text x="600" y="655" text-anchor="middle" class="main-node">技术实现</text>
  
  <!-- 技术实现子节点 -->
  <line x1="600" y1="670" x2="400" y2="720" class="line"/>
  <rect x="340" y="705" width="120" height="30" class="detail-rect"/>
  <text x="400" y="725" text-anchor="middle" class="sub-node">HTML5 Canvas</text>
  
  <line x1="600" y1="670" x2="520" y2="720" class="line"/>
  <rect x="460" y="705" width="120" height="30" class="detail-rect"/>
  <text x="520" y="725" text-anchor="middle" class="sub-node">JavaScript ES6</text>
  
  <line x1="600" y1="670" x2="680" y2="720" class="line"/>
  <rect x="620" y="705" width="120" height="30" class="detail-rect"/>
  <text x="680" y="725" text-anchor="middle" class="sub-node">CSS3动画</text>
  
  <line x1="600" y1="670" x2="800" y2="720" class="line"/>
  <rect x="740" y="705" width="120" height="30" class="detail-rect"/>
  <text x="800" y="725" text-anchor="middle" class="sub-node">Web Audio API</text>
  
  <!-- 详细功能说明 -->
  <text x="50" y="750" class="detail-node">• 支持单机模式和联网对战</text>
  <text x="50" y="770" class="detail-node">• 响应式设计，适配移动端</text>
  <text x="300" y="750" class="detail-node">• 完整的游戏统计和排行榜</text>
  <text x="300" y="770" class="detail-node">• 多种皮肤和主题选择</text>
  <text x="550" y="750" class="detail-node">• 游戏回放和复盘功能</text>
  <text x="550" y="770" class="detail-node">• 社交分享和成就系统</text>
  <text x="800" y="750" class="detail-node">• 离线模式和数据同步</text>
  <text x="800" y="770" class="detail-node">• 多语言支持和本地化</text>
  
</svg>