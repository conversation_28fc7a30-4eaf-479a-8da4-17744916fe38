<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>斗地主游戏测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .test-pass {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-fail {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .card-display {
            display: inline-block;
            margin: 5px;
            border: 1px solid #ccc;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>斗地主游戏测试页面</h1>
    
    <div class="test-section">
        <h2>1. 基础组件测试</h2>
        <button onclick="testBasicComponents()">运行基础测试</button>
        <div id="basic-test-results"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 卡牌系统测试</h2>
        <button onclick="testCardSystem()">测试卡牌系统</button>
        <div id="card-test-results"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 牌型识别测试</h2>
        <button onclick="testPatternRecognition()">测试牌型识别</button>
        <div id="pattern-test-results"></div>
    </div>
    
    <div class="test-section">
        <h2>4. 卡牌渲染测试</h2>
        <button onclick="testCardRendering()">测试卡牌渲染</button>
        <div id="render-test-results"></div>
    </div>
    
    <div class="test-section">
        <h2>5. AI系统测试</h2>
        <button onclick="testAISystem()">测试AI系统</button>
        <div id="ai-test-results"></div>
    </div>
    
    <div class="test-section">
        <h2>6. 游戏逻辑测试</h2>
        <button onclick="testGameLogic()">测试游戏逻辑</button>
        <div id="game-test-results"></div>
    </div>

    <!-- 加载游戏脚本 -->
    <script src="js/utils.js"></script>
    <script src="js/card.js"></script>
    <script src="js/deck.js"></script>
    <script src="js/player.js"></script>
    <script src="js/ai.js"></script>
    <script src="js/game.js"></script>
    <script src="js/cardRenderer.js"></script>
    <script src="js/audio.js"></script>

    <script>
        // 测试结果显示函数
        function showTestResult(containerId, testName, passed, message = '') {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = passed ? 'test-result test-pass' : 'test-result test-fail';
            resultDiv.innerHTML = `
                <strong>${testName}:</strong> ${passed ? '✓ 通过' : '✗ 失败'}
                ${message ? `<br><small>${message}</small>` : ''}
            `;
            container.appendChild(resultDiv);
        }

        // 1. 基础组件测试
        function testBasicComponents() {
            const container = document.getElementById('basic-test-results');
            container.innerHTML = '';

            try {
                // 测试工具函数
                const shuffled = Utils.shuffle([1, 2, 3, 4, 5]);
                showTestResult('basic-test-results', '工具函数', 
                    Array.isArray(shuffled) && shuffled.length === 5, 
                    '洗牌函数正常工作');

                // 测试事件发射器
                const emitter = new Utils.EventEmitter();
                let eventFired = false;
                emitter.on('test', () => { eventFired = true; });
                emitter.emit('test');
                showTestResult('basic-test-results', '事件系统', 
                    eventFired, '事件发射器正常工作');

                // 测试本地存储
                Utils.storage.set('test', { value: 123 });
                const stored = Utils.storage.get('test');
                showTestResult('basic-test-results', '本地存储', 
                    stored && stored.value === 123, '本地存储正常工作');

            } catch (error) {
                showTestResult('basic-test-results', '基础组件', false, error.message);
            }
        }

        // 2. 卡牌系统测试
        function testCardSystem() {
            const container = document.getElementById('card-test-results');
            container.innerHTML = '';

            try {
                // 测试卡牌创建
                const card = new Card('A', 'spades');
                showTestResult('card-test-results', '卡牌创建', 
                    card.value === 'A' && card.suit === 'spades', 
                    '卡牌对象创建成功');

                // 测试卡牌比较
                const card2 = new Card('K', 'hearts');
                showTestResult('card-test-results', '卡牌比较', 
                    card.compareTo(card2) > 0, 
                    'A > K 比较正确');

                // 测试牌组
                const deck = new Deck();
                showTestResult('card-test-results', '牌组创建', 
                    deck.getRemainingCount() === 54, 
                    '标准牌组包含54张牌');

                // 测试洗牌
                const originalOrder = deck.getAllCards().map(c => c.id);
                deck.shuffle();
                const shuffledOrder = deck.getAllCards().map(c => c.id);
                showTestResult('card-test-results', '洗牌功能', 
                    JSON.stringify(originalOrder) !== JSON.stringify(shuffledOrder), 
                    '洗牌改变了卡牌顺序');

                // 测试发牌
                const dealtCards = deck.dealCards(17);
                showTestResult('card-test-results', '发牌功能', 
                    dealtCards.length === 17 && deck.getRemainingCount() === 37, 
                    '发牌数量正确');

            } catch (error) {
                showTestResult('card-test-results', '卡牌系统', false, error.message);
            }
        }

        // 3. 牌型识别测试
        function testPatternRecognition() {
            const container = document.getElementById('pattern-test-results');
            container.innerHTML = '';

            try {
                // 测试单牌
                const singleCard = [new Card('A', 'spades')];
                const singlePattern = CardPattern.recognize(singleCard);
                showTestResult('pattern-test-results', '单牌识别', 
                    singlePattern && singlePattern.type === CardPattern.TYPES.SINGLE, 
                    '单牌识别正确');

                // 测试对子
                const pairCards = [new Card('K', 'spades'), new Card('K', 'hearts')];
                const pairPattern = CardPattern.recognize(pairCards);
                showTestResult('pattern-test-results', '对子识别', 
                    pairPattern && pairPattern.type === CardPattern.TYPES.PAIR, 
                    '对子识别正确');

                // 测试三张
                const tripleCards = [new Card('Q', 'spades'), new Card('Q', 'hearts'), new Card('Q', 'clubs')];
                const triplePattern = CardPattern.recognize(tripleCards);
                showTestResult('pattern-test-results', '三张识别', 
                    triplePattern && triplePattern.type === CardPattern.TYPES.TRIPLE, 
                    '三张识别正确');

                // 测试炸弹
                const bombCards = [new Card('J', 'spades'), new Card('J', 'hearts'), new Card('J', 'clubs'), new Card('J', 'diamonds')];
                const bombPattern = CardPattern.recognize(bombCards);
                showTestResult('pattern-test-results', '炸弹识别', 
                    bombPattern && bombPattern.type === CardPattern.TYPES.BOMB, 
                    '炸弹识别正确');

                // 测试火箭
                const rocketCards = [new Card('JOKER_SMALL'), new Card('JOKER_BIG')];
                const rocketPattern = CardPattern.recognize(rocketCards);
                showTestResult('pattern-test-results', '火箭识别', 
                    rocketPattern && rocketPattern.type === CardPattern.TYPES.ROCKET, 
                    '火箭识别正确');

            } catch (error) {
                showTestResult('pattern-test-results', '牌型识别', false, error.message);
            }
        }

        // 4. 卡牌渲染测试
        function testCardRendering() {
            const container = document.getElementById('render-test-results');
            container.innerHTML = '';

            try {
                const renderer = new CardRenderer();
                
                // 测试普通卡牌渲染
                const card = new Card('A', 'spades');
                const canvas = renderer.renderCard(card, true);
                showTestResult('render-test-results', '卡牌渲染', 
                    canvas instanceof HTMLCanvasElement, 
                    '卡牌渲染返回Canvas对象');

                // 显示渲染的卡牌
                const displayDiv = document.createElement('div');
                displayDiv.innerHTML = '<br>渲染示例：';
                
                // 渲染几张不同的卡牌
                const testCards = [
                    new Card('A', 'spades'),
                    new Card('K', 'hearts'),
                    new Card('Q', 'diamonds'),
                    new Card('J', 'clubs'),
                    new Card('JOKER_SMALL'),
                    new Card('JOKER_BIG')
                ];

                testCards.forEach(testCard => {
                    const testCanvas = renderer.renderCard(testCard, true);
                    testCanvas.className = 'card-display';
                    displayDiv.appendChild(testCanvas);
                });

                container.appendChild(displayDiv);

                showTestResult('render-test-results', '多卡牌渲染', true, 
                    '成功渲染多种卡牌类型');

            } catch (error) {
                showTestResult('render-test-results', '卡牌渲染', false, error.message);
            }
        }

        // 5. AI系统测试
        function testAISystem() {
            const container = document.getElementById('ai-test-results');
            container.innerHTML = '';

            try {
                // 创建AI玩家
                const aiPlayer = new AIPlayer('ai1', 'AI测试', AIPlayer.DIFFICULTY.MEDIUM);
                showTestResult('ai-test-results', 'AI玩家创建', 
                    aiPlayer instanceof AIPlayer && aiPlayer.isAI(), 
                    'AI玩家创建成功');

                // 给AI玩家一些卡牌
                const testCards = [
                    new Card('A', 'spades'), new Card('K', 'hearts'), 
                    new Card('Q', 'diamonds'), new Card('J', 'clubs')
                ];
                aiPlayer.addCards(testCards);
                
                showTestResult('ai-test-results', 'AI手牌管理', 
                    aiPlayer.getHandCount() === 4, 
                    'AI玩家手牌管理正常');

                // 测试AI手牌强度评估
                const strength = aiPlayer.evaluateHandStrength();
                showTestResult('ai-test-results', 'AI手牌评估', 
                    typeof strength === 'number' && strength >= 0, 
                    `手牌强度评估: ${strength}`);

                // 测试AI可出牌型
                const patterns = aiPlayer.getAllPossiblePatterns();
                showTestResult('ai-test-results', 'AI牌型分析', 
                    Array.isArray(patterns) && patterns.length > 0, 
                    `找到 ${patterns.length} 种可出牌型`);

            } catch (error) {
                showTestResult('ai-test-results', 'AI系统', false, error.message);
            }
        }

        // 6. 游戏逻辑测试
        function testGameLogic() {
            const container = document.getElementById('game-test-results');
            container.innerHTML = '';

            try {
                // 创建游戏实例
                const game = new DouDiZhuGame();
                showTestResult('game-test-results', '游戏创建', 
                    game instanceof DouDiZhuGame, 
                    '游戏实例创建成功');

                // 创建测试玩家
                const players = [
                    new Player('player1', '玩家1', Player.TYPES.HUMAN),
                    new AIPlayer('ai1', 'AI1', AIPlayer.DIFFICULTY.EASY),
                    new AIPlayer('ai2', 'AI2', AIPlayer.DIFFICULTY.EASY)
                ];

                // 初始化游戏
                game.initialize(players);
                showTestResult('game-test-results', '游戏初始化', 
                    game.players.length === 3, 
                    '游戏初始化成功，包含3个玩家');

                // 测试游戏状态
                const gameState = game.getGameState();
                showTestResult('game-test-results', '游戏状态', 
                    gameState && gameState.state === DouDiZhuGame.STATES.WAITING, 
                    '游戏状态正确');

                showTestResult('game-test-results', '游戏逻辑', true, 
                    '所有游戏逻辑测试通过');

            } catch (error) {
                showTestResult('game-test-results', '游戏逻辑', false, error.message);
            }
        }

        // 页面加载完成后显示提示
        document.addEventListener('DOMContentLoaded', function() {
            console.log('斗地主游戏测试页面加载完成');
            console.log('点击各个测试按钮来验证游戏功能');
        });
    </script>
</body>
</html>
