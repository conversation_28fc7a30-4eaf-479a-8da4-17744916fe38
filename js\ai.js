import Player from './player.js';
import Utils from './utils.js';
import { CardPattern } from './deck.js';

/**
 * AI玩家类 - 智能对手系统
 */
export class AIPlayer extends Player {
    // AI难度等级
    static DIFFICULTY = {
        EASY: 'easy',
        MEDIUM: 'medium',
        HARD: 'hard'
    };

    // AI策略类型
    static STRATEGY = {
        AGGRESSIVE: 'aggressive',    // 激进型
        CONSERVATIVE: 'conservative', // 保守型
        BALANCED: 'balanced'         // 平衡型
    };

    /**
     * 构造函数
     * @param {string} id 玩家ID
     * @param {string} name 玩家名称
     * @param {string} difficulty 难度等级
     * @param {string} strategy 策略类型
     */
    constructor(id, name, difficulty = AIPlayer.DIFFICULTY.MEDIUM, strategy = AIPlayer.STRATEGY.BALANCED) {
        super(id, name, Player.TYPES.AI);
        this.difficulty = difficulty;
        this.strategy = strategy;
        this.memory = new AIMemory();
        this.decisionDelay = this.getDecisionDelay();
        this.thinkingTime = 0;
    }

    /**
     * 获取决策延迟时间
     * @returns {number} 延迟时间（毫秒）
     */
    getDecisionDelay() {
        const delays = {
            [AIPlayer.DIFFICULTY.EASY]: { min: 1000, max: 3000 },
            [AIPlayer.DIFFICULTY.MEDIUM]: { min: 800, max: 2000 },
            [AIPlayer.DIFFICULTY.HARD]: { min: 500, max: 1500 }
        };
        
        const range = delays[this.difficulty];
        return Utils.randomInt(range.min, range.max);
    }

    /**
     * AI决定是否叫地主
     * @param {Card[]} landlordCards 地主牌
     * @param {Player[]} otherPlayers 其他玩家
     * @returns {Promise<boolean>} 是否叫地主
     */
    async decideCallLandlord(landlordCards = [], otherPlayers = []) {
        this.thinkingTime = this.getDecisionDelay();
        await Utils.delay(this.thinkingTime);

        const handStrength = this.evaluateHandStrength();
        const landlordCardsStrength = this.evaluateCards(landlordCards);
        
        let callProbability = 0;

        // 基础概率计算
        if (handStrength >= 80) callProbability += 0.8;
        else if (handStrength >= 60) callProbability += 0.5;
        else if (handStrength >= 40) callProbability += 0.2;

        // 地主牌加成
        callProbability += landlordCardsStrength * 0.01;

        // 难度调整
        switch (this.difficulty) {
            case AIPlayer.DIFFICULTY.EASY:
                callProbability *= 0.7; // 更保守
                break;
            case AIPlayer.DIFFICULTY.HARD:
                callProbability *= 1.2; // 更激进
                break;
        }

        // 策略调整
        switch (this.strategy) {
            case AIPlayer.STRATEGY.AGGRESSIVE:
                callProbability *= 1.3;
                break;
            case AIPlayer.STRATEGY.CONSERVATIVE:
                callProbability *= 0.8;
                break;
        }

        return Math.random() < Math.min(callProbability, 0.9);
    }

    /**
     * AI决定出牌
     * @param {Object|null} lastPattern 上一个牌型
     * @param {Player[]} otherPlayers 其他玩家
     * @returns {Promise<Card[]|null>} 要出的牌，null表示不出
     */
    async decidePlayCards(lastPattern = null, otherPlayers = []) {
        this.thinkingTime = this.getDecisionDelay();
        await Utils.delay(this.thinkingTime);

        const playablePatterns = this.getPlayablePatterns(lastPattern);
        
        if (playablePatterns.length === 0) {
            return null; // 无牌可出
        }

        // 根据策略选择出牌
        const selectedPattern = this.selectBestPattern(playablePatterns, lastPattern, otherPlayers);
        return selectedPattern ? selectedPattern.cards : null;
    }

    /**
     * 选择最佳牌型
     * @param {Object[]} patterns 可出的牌型
     * @param {Object|null} lastPattern 上一个牌型
     * @param {Player[]} otherPlayers 其他玩家
     * @returns {Object|null} 选择的牌型
     */
    selectBestPattern(patterns, lastPattern, otherPlayers) {
        if (patterns.length === 0) return null;

        // 按策略排序牌型
        const sortedPatterns = this.sortPatternsByStrategy(patterns, lastPattern, otherPlayers);
        
        // 根据难度选择
        let selectedIndex = 0;
        switch (this.difficulty) {
            case AIPlayer.DIFFICULTY.EASY:
                // 简单AI随机选择前50%的牌型
                selectedIndex = Utils.randomInt(0, Math.floor(sortedPatterns.length * 0.5));
                break;
            case AIPlayer.DIFFICULTY.MEDIUM:
                // 中等AI选择前30%的牌型
                selectedIndex = Utils.randomInt(0, Math.floor(sortedPatterns.length * 0.3));
                break;
            case AIPlayer.DIFFICULTY.HARD:
                // 困难AI选择最优牌型
                selectedIndex = 0;
                break;
        }

        return sortedPatterns[selectedIndex];
    }

    /**
     * 按策略排序牌型
     * @param {Object[]} patterns 牌型数组
     * @param {Object|null} lastPattern 上一个牌型
     * @param {Player[]} otherPlayers 其他玩家
     * @returns {Object[]} 排序后的牌型数组
     */
    sortPatternsByStrategy(patterns, lastPattern, otherPlayers) {
        return patterns.sort((a, b) => {
            const scoreA = this.evaluatePatternScore(a, lastPattern, otherPlayers);
            const scoreB = this.evaluatePatternScore(b, lastPattern, otherPlayers);
            return scoreB - scoreA; // 降序排列
        });
    }

    /**
     * 评估牌型分数
     * @param {Object} pattern 牌型
     * @param {Object|null} lastPattern 上一个牌型
     * @param {Player[]} otherPlayers 其他玩家
     * @returns {number} 牌型分数
     */
    evaluatePatternScore(pattern, lastPattern, otherPlayers) {
        let score = 0;

        // 基础分数：优先出小牌
        score += (20 - pattern.weight) * 2;

        // 牌型大小奖励
        if (pattern.cards.length > 1) {
            score += pattern.cards.length * 5;
        }

        // 特殊牌型奖励
        if (pattern.type === CardPattern.TYPES.BOMB) {
            score += 50;
        } else if (pattern.type === CardPattern.TYPES.ROCKET) {
            score += 100;
        }

        // 策略调整
        switch (this.strategy) {
            case AIPlayer.STRATEGY.AGGRESSIVE:
                // 激进策略：优先出大牌和特殊牌型
                if (pattern.weight > 12) score += 20;
                if (pattern.type === CardPattern.TYPES.BOMB || pattern.type === CardPattern.TYPES.ROCKET) {
                    score += 30;
                }
                break;
                
            case AIPlayer.STRATEGY.CONSERVATIVE:
                // 保守策略：优先出小牌，保留大牌
                if (pattern.weight < 10) score += 15;
                if (pattern.type === CardPattern.TYPES.BOMB || pattern.type === CardPattern.TYPES.ROCKET) {
                    score -= 20; // 保留炸弹和火箭
                }
                break;
                
            case AIPlayer.STRATEGY.BALANCED:
                // 平衡策略：综合考虑
                if (pattern.weight >= 8 && pattern.weight <= 12) score += 10;
                break;
        }

        // 手牌数量考虑
        const remainingCards = this.getHandCount() - pattern.cards.length;
        if (remainingCards <= 3) {
            score += 30; // 接近胜利时优先出牌
        }

        // 对手手牌数量考虑
        const minOpponentCards = Math.min(...otherPlayers.map(p => p.getHandCount()));
        if (minOpponentCards <= 5) {
            // 对手快要赢了，优先出大牌阻止
            if (pattern.weight > 12) score += 25;
        }

        return score;
    }

    /**
     * 评估手牌强度
     * @returns {number} 手牌强度分数 (0-100)
     */
    evaluateHandStrength() {
        let strength = 0;
        const valueCounts = this.getValueCounts();

        // 统计各种牌型
        let pairs = 0, triples = 0, bombs = 0;
        let hasRocket = false;

        Object.entries(valueCounts).forEach(([value, count]) => {
            if (count === 2) pairs++;
            else if (count === 3) triples++;
            else if (count === 4) bombs++;
        });

        // 检查火箭
        if (this.hand.some(c => c.value === 'JOKER_SMALL') && 
            this.hand.some(c => c.value === 'JOKER_BIG')) {
            hasRocket = true;
        }

        // 计算强度
        strength += pairs * 5;
        strength += triples * 10;
        strength += bombs * 25;
        if (hasRocket) strength += 30;

        // 大牌奖励
        const bigCards = this.hand.filter(c => c.getWeight() >= 12).length;
        strength += bigCards * 3;

        // 顺子潜力
        const straightPotential = this.evaluateStraightPotential();
        strength += straightPotential * 2;

        return Math.min(strength, 100);
    }

    /**
     * 评估顺子潜力
     * @returns {number} 顺子潜力分数
     */
    evaluateStraightPotential() {
        const weights = this.hand
            .filter(c => c.getWeight() < 15) // 排除2和王
            .map(c => c.getWeight())
            .sort((a, b) => a - b);

        let maxConsecutive = 0;
        let currentConsecutive = 1;

        for (let i = 1; i < weights.length; i++) {
            if (weights[i] === weights[i-1] + 1) {
                currentConsecutive++;
            } else {
                maxConsecutive = Math.max(maxConsecutive, currentConsecutive);
                currentConsecutive = 1;
            }
        }

        return Math.max(maxConsecutive, currentConsecutive);
    }

    /**
     * 评估卡牌价值
     * @param {Card[]} cards 卡牌数组
     * @returns {number} 价值分数
     */
    evaluateCards(cards) {
        if (!cards || cards.length === 0) return 0;

        let value = 0;
        cards.forEach(card => {
            if (card.isJoker()) {
                value += card.value === 'JOKER_BIG' ? 20 : 15;
            } else {
                value += Math.max(1, card.getWeight() - 10);
            }
        });

        return value;
    }

    /**
     * 获取牌面值统计
     * @returns {Object} 牌面值统计
     */
    getValueCounts() {
        const counts = {};
        this.hand.forEach(card => {
            const value = card.value;
            counts[value] = (counts[value] || 0) + 1;
        });
        return counts;
    }

    /**
     * 更新记忆系统
     * @param {string} event 事件类型
     * @param {*} data 事件数据
     */
    updateMemory(event, data) {
        this.memory.record(event, data);
    }

    /**
     * 设置难度等级
     * @param {string} difficulty 难度等级
     */
    setDifficulty(difficulty) {
        this.difficulty = difficulty;
        this.decisionDelay = this.getDecisionDelay();
    }

    /**
     * 设置策略类型
     * @param {string} strategy 策略类型
     */
    setStrategy(strategy) {
        this.strategy = strategy;
    }
}

/**
 * AI记忆系统
 */
export class AIMemory {
    constructor() {
        this.gameHistory = [];
        this.playerPatterns = new Map();
        this.cardCounts = new Map();
    }

    /**
     * 记录事件
     * @param {string} event 事件类型
     * @param {*} data 事件数据
     */
    record(event, data) {
        this.gameHistory.push({
            event: event,
            data: data,
            timestamp: Date.now()
        });

        // 根据事件类型更新相应的记忆
        switch (event) {
            case 'cardPlayed':
                this.recordCardPlay(data);
                break;
            case 'playerPassed':
                this.recordPlayerPass(data);
                break;
        }
    }

    /**
     * 记录玩家出牌
     * @param {Object} data 出牌数据
     */
    recordCardPlay(data) {
        const playerId = data.player.id;
        if (!this.playerPatterns.has(playerId)) {
            this.playerPatterns.set(playerId, []);
        }
        this.playerPatterns.get(playerId).push(data.pattern);
    }

    /**
     * 记录玩家过牌
     * @param {Object} data 过牌数据
     */
    recordPlayerPass(data) {
        // 可以记录玩家的过牌习惯
    }

    /**
     * 获取玩家的出牌模式
     * @param {string} playerId 玩家ID
     * @returns {Object[]} 出牌模式数组
     */
    getPlayerPatterns(playerId) {
        return this.playerPatterns.get(playerId) || [];
    }

    /**
     * 清空记忆
     */
    clear() {
        this.gameHistory = [];
        this.playerPatterns.clear();
        this.cardCounts.clear();
    }
}
