import Card from './card.js';
import Utils from './utils.js';

/**
 * 牌组类 - 管理一副完整的扑克牌
 */
export class Deck {
    /**
     * 构造函数
     */
    constructor() {
        this.cards = [];
        this.initializeDeck();
    }

    /**
     * 初始化一副完整的扑克牌（54张）
     */
    initializeDeck() {
        this.cards = [];
        
        // 添加普通牌（52张）
        const suits = Object.values(Card.SUITS);
        const values = [3, 4, 5, 6, 7, 8, 9, 10, 'J', 'Q', 'K', 'A', 2];
        
        for (const suit of suits) {
            for (const value of values) {
                this.cards.push(new Card(value, suit));
            }
        }
        
        // 添加大小王（2张）
        this.cards.push(new Card('JOKER_SMALL'));
        this.cards.push(new Card('JOKER_BIG'));
    }

    /**
     * 洗牌
     * @returns {Deck} 返回自身以支持链式调用
     */
    shuffle() {
        this.cards = Utils.shuffle(this.cards);
        return this;
    }

    /**
     * 发一张牌
     * @returns {Card|null} 发出的卡牌，如果没有牌则返回null
     */
    dealCard() {
        return this.cards.length > 0 ? this.cards.pop() : null;
    }

    /**
     * 发多张牌
     * @param {number} count 发牌数量
     * @returns {Card[]} 发出的卡牌数组
     */
    dealCards(count) {
        const dealtCards = [];
        for (let i = 0; i < count && this.cards.length > 0; i++) {
            dealtCards.push(this.dealCard());
        }
        return dealtCards;
    }

    /**
     * 获取剩余牌数
     * @returns {number} 剩余牌数
     */
    getRemainingCount() {
        return this.cards.length;
    }

    /**
     * 判断是否还有牌
     * @returns {boolean} 是否还有牌
     */
    hasCards() {
        return this.cards.length > 0;
    }

    /**
     * 重置牌组（重新初始化并洗牌）
     * @returns {Deck} 返回自身以支持链式调用
     */
    reset() {
        this.initializeDeck();
        this.shuffle();
        return this;
    }

    /**
     * 添加卡牌到牌组底部
     * @param {Card} card 要添加的卡牌
     * @returns {Deck} 返回自身以支持链式调用
     */
    addCard(card) {
        this.cards.unshift(card);
        return this;
    }

    /**
     * 添加多张卡牌到牌组底部
     * @param {Card[]} cards 要添加的卡牌数组
     * @returns {Deck} 返回自身以支持链式调用
     */
    addCards(cards) {
        this.cards.unshift(...cards);
        return this;
    }

    /**
     * 查看顶部的牌（不移除）
     * @returns {Card|null} 顶部的卡牌，如果没有牌则返回null
     */
    peek() {
        return this.cards.length > 0 ? this.cards[this.cards.length - 1] : null;
    }

    /**
     * 查看顶部的多张牌（不移除）
     * @param {number} count 查看数量
     * @returns {Card[]} 顶部的卡牌数组
     */
    peekCards(count) {
        const startIndex = Math.max(0, this.cards.length - count);
        return this.cards.slice(startIndex);
    }

    /**
     * 获取所有卡牌的副本
     * @returns {Card[]} 所有卡牌的副本数组
     */
    getAllCards() {
        return this.cards.map(card => card.clone());
    }

    /**
     * 清空牌组
     * @returns {Deck} 返回自身以支持链式调用
     */
    clear() {
        this.cards = [];
        return this;
    }

    /**
     * 检查牌组是否包含指定卡牌
     * @param {Card} targetCard 目标卡牌
     * @returns {boolean} 是否包含
     */
    contains(targetCard) {
        return this.cards.some(card => card.equals(targetCard));
    }

    /**
     * 移除指定卡牌
     * @param {Card} targetCard 要移除的卡牌
     * @returns {boolean} 是否成功移除
     */
    removeCard(targetCard) {
        const index = this.cards.findIndex(card => card.equals(targetCard));
        if (index !== -1) {
            this.cards.splice(index, 1);
            return true;
        }
        return false;
    }

    /**
     * 按权重排序卡牌（从小到大）
     * @returns {Deck} 返回自身以支持链式调用
     */
    sort() {
        this.cards.sort((a, b) => a.compareTo(b));
        return this;
    }

    /**
     * 按权重倒序排序卡牌（从大到小）
     * @returns {Deck} 返回自身以支持链式调用
     */
    sortDescending() {
        this.cards.sort((a, b) => b.compareTo(a));
        return this;
    }

    /**
     * 按花色分组
     * @returns {Object} 按花色分组的卡牌对象
     */
    groupBySuit() {
        const groups = {
            [Card.SUITS.SPADES]: [],
            [Card.SUITS.HEARTS]: [],
            [Card.SUITS.DIAMONDS]: [],
            [Card.SUITS.CLUBS]: [],
            jokers: []
        };

        this.cards.forEach(card => {
            if (card.isJoker()) {
                groups.jokers.push(card);
            } else {
                groups[card.suit].push(card);
            }
        });

        return groups;
    }

    /**
     * 按牌面值分组
     * @returns {Object} 按牌面值分组的卡牌对象
     */
    groupByValue() {
        const groups = {};
        
        this.cards.forEach(card => {
            const value = card.value;
            if (!groups[value]) {
                groups[value] = [];
            }
            groups[value].push(card);
        });

        return groups;
    }

    /**
     * 获取指定牌面值的所有卡牌
     * @param {string|number} value 牌面值
     * @returns {Card[]} 指定牌面值的卡牌数组
     */
    getCardsByValue(value) {
        return this.cards.filter(card => card.value === value);
    }

    /**
     * 获取指定花色的所有卡牌
     * @param {string} suit 花色
     * @returns {Card[]} 指定花色的卡牌数组
     */
    getCardsBySuit(suit) {
        return this.cards.filter(card => card.suit === suit);
    }

    /**
     * 统计各牌面值的数量
     * @returns {Object} 牌面值统计对象
     */
    getValueCounts() {
        const counts = {};
        this.cards.forEach(card => {
            const value = card.value;
            counts[value] = (counts[value] || 0) + 1;
        });
        return counts;
    }

    /**
     * 转换为JSON对象
     * @returns {Object} JSON对象
     */
    toJSON() {
        return {
            cards: this.cards.map(card => card.toJSON())
        };
    }

    /**
     * 从JSON对象创建牌组
     * @param {Object} json JSON对象
     * @returns {Deck} 牌组实例
     */
    static fromJSON(json) {
        const deck = new Deck();
        deck.cards = json.cards.map(cardJson => Card.fromJSON(cardJson));
        return deck;
    }

    /**
     * 创建空牌组
     * @returns {Deck} 空牌组实例
     */
    static createEmpty() {
        const deck = new Deck();
        deck.clear();
        return deck;
    }

    /**
     * 转换为字符串表示
     * @returns {string} 字符串表示
     */
    toString() {
        return `Deck(${this.cards.length} cards): [${this.cards.map(card => card.toString()).join(', ')}]`;
    }
}



/**
 * 牌型识别器 - 识别斗地主中的各种牌型
 */
export class CardPattern {
    // 牌型常量
    static TYPES = {
        SINGLE: 'single',           // 单牌
        PAIR: 'pair',              // 对子
        TRIPLE: 'triple',          // 三张
        TRIPLE_WITH_SINGLE: 'triple_with_single',  // 三带一
        TRIPLE_WITH_PAIR: 'triple_with_pair',      // 三带二
        STRAIGHT: 'straight',      // 顺子
        PAIR_STRAIGHT: 'pair_straight',            // 连对
        TRIPLE_STRAIGHT: 'triple_straight',        // 飞机
        TRIPLE_STRAIGHT_WITH_SINGLES: 'triple_straight_with_singles',  // 飞机带单牌
        TRIPLE_STRAIGHT_WITH_PAIRS: 'triple_straight_with_pairs',      // 飞机带对子
        FOUR_WITH_TWO_SINGLES: 'four_with_two_singles',  // 四带二单
        FOUR_WITH_TWO_PAIRS: 'four_with_two_pairs',      // 四带二对
        BOMB: 'bomb',              // 炸弹
        ROCKET: 'rocket'           // 火箭（双王）
    };

    /**
     * 识别牌型
     * @param {Card[]} cards 卡牌数组
     * @returns {Object|null} 牌型信息对象，如果无法识别则返回null
     */
    static recognize(cards) {
        if (!cards || cards.length === 0) return null;

        // 按权重排序
        const sortedCards = [...cards].sort((a, b) => a.compareTo(b));
        const valueCounts = this.getValueCounts(sortedCards);
        const counts = Object.values(valueCounts);
        const values = Object.keys(valueCounts).map(v => parseInt(v) || v);

        // 根据卡牌数量和组合判断牌型
        switch (cards.length) {
            case 1:
                return this.recognizeSingle(sortedCards[0]);
            case 2:
                return this.recognizeTwo(sortedCards, valueCounts);
            case 3:
                return this.recognizeThree(sortedCards, valueCounts);
            case 4:
                return this.recognizeFour(sortedCards, valueCounts);
            case 5:
                return this.recognizeFive(sortedCards, valueCounts);
            default:
                return this.recognizeMultiple(sortedCards, valueCounts);
        }
    }

    /**
     * 识别单牌
     */
    static recognizeSingle(card) {
        return {
            type: this.TYPES.SINGLE,
            weight: card.getWeight(),
            cards: [card],
            description: `单牌 ${card.getFullDisplayText()}`
        };
    }

    /**
     * 识别两张牌的组合
     */
    static recognizeTwo(cards, valueCounts) {
        const counts = Object.values(valueCounts);

        // 对子
        if (counts.length === 1 && counts[0] === 2) {
            return {
                type: this.TYPES.PAIR,
                weight: cards[0].getWeight(),
                cards: cards,
                description: `对子 ${cards[0].getDisplayName()}`
            };
        }

        // 火箭（双王）
        if (cards.length === 2 &&
            cards.some(c => c.value === 'JOKER_SMALL') &&
            cards.some(c => c.value === 'JOKER_BIG')) {
            return {
                type: this.TYPES.ROCKET,
                weight: 1000, // 最高权重
                cards: cards,
                description: '火箭'
            };
        }

        return null;
    }

    /**
     * 识别三张牌的组合
     */
    static recognizeThree(cards, valueCounts) {
        const counts = Object.values(valueCounts);

        // 三张
        if (counts.length === 1 && counts[0] === 3) {
            return {
                type: this.TYPES.TRIPLE,
                weight: cards[0].getWeight(),
                cards: cards,
                description: `三张 ${cards[0].getDisplayName()}`
            };
        }

        return null;
    }

    /**
     * 识别四张牌的组合
     */
    static recognizeFour(cards, valueCounts) {
        const counts = Object.values(valueCounts);

        // 炸弹
        if (counts.length === 1 && counts[0] === 4) {
            return {
                type: this.TYPES.BOMB,
                weight: cards[0].getWeight() + 100, // 炸弹权重加100
                cards: cards,
                description: `炸弹 ${cards[0].getDisplayName()}`
            };
        }

        // 三带一
        if (counts.length === 2 && counts.includes(3) && counts.includes(1)) {
            const tripleValue = Object.keys(valueCounts).find(v => valueCounts[v] === 3);
            const tripleWeight = Card.VALUES[tripleValue];
            return {
                type: this.TYPES.TRIPLE_WITH_SINGLE,
                weight: tripleWeight,
                cards: cards,
                description: `三带一 ${Card.DISPLAY_NAMES[tripleValue]}`
            };
        }

        return null;
    }

    /**
     * 识别五张牌的组合
     */
    static recognizeFive(cards, valueCounts) {
        const counts = Object.values(valueCounts);

        // 三带二
        if (counts.length === 2 && counts.includes(3) && counts.includes(2)) {
            const tripleValue = Object.keys(valueCounts).find(v => valueCounts[v] === 3);
            const tripleWeight = Card.VALUES[tripleValue];
            return {
                type: this.TYPES.TRIPLE_WITH_PAIR,
                weight: tripleWeight,
                cards: cards,
                description: `三带二 ${Card.DISPLAY_NAMES[tripleValue]}`
            };
        }

        // 顺子
        const straightResult = this.checkStraight(cards);
        if (straightResult) {
            return straightResult;
        }

        return null;
    }

    /**
     * 识别多张牌的复杂组合
     */
    static recognizeMultiple(cards, valueCounts) {
        // 检查顺子
        const straightResult = this.checkStraight(cards);
        if (straightResult) return straightResult;

        // 检查连对
        const pairStraightResult = this.checkPairStraight(cards, valueCounts);
        if (pairStraightResult) return pairStraightResult;

        // 检查飞机相关牌型
        const airplaneResult = this.checkAirplane(cards, valueCounts);
        if (airplaneResult) return airplaneResult;

        // 检查四带二
        const fourWithTwoResult = this.checkFourWithTwo(cards, valueCounts);
        if (fourWithTwoResult) return fourWithTwoResult;

        return null;
    }

    /**
     * 检查是否为顺子
     */
    static checkStraight(cards) {
        if (cards.length < 5) return null;

        // 顺子不能包含2和王
        if (cards.some(c => c.getWeight() >= 15)) return null;

        const weights = cards.map(c => c.getWeight()).sort((a, b) => a - b);

        // 检查是否连续
        for (let i = 1; i < weights.length; i++) {
            if (weights[i] !== weights[i-1] + 1) return null;
        }

        return {
            type: this.TYPES.STRAIGHT,
            weight: weights[0],
            cards: cards,
            description: `顺子 ${cards.length}张`
        };
    }

    /**
     * 检查是否为连对
     */
    static checkPairStraight(cards, valueCounts) {
        if (cards.length < 6 || cards.length % 2 !== 0) return null;

        const counts = Object.values(valueCounts);
        if (!counts.every(count => count === 2)) return null;

        const values = Object.keys(valueCounts).map(v => Card.VALUES[v]).sort((a, b) => a - b);

        // 连对不能包含2和王
        if (values.some(v => v >= 15)) return null;

        // 检查是否连续
        for (let i = 1; i < values.length; i++) {
            if (values[i] !== values[i-1] + 1) return null;
        }

        return {
            type: this.TYPES.PAIR_STRAIGHT,
            weight: values[0],
            cards: cards,
            description: `连对 ${values.length}对`
        };
    }

    /**
     * 检查飞机相关牌型
     */
    static checkAirplane(cards, valueCounts) {
        const triples = Object.keys(valueCounts).filter(v => valueCounts[v] === 3);
        if (triples.length < 2) return null;

        const tripleWeights = triples.map(v => Card.VALUES[v]).sort((a, b) => a - b);

        // 检查三张是否连续
        for (let i = 1; i < tripleWeights.length; i++) {
            if (tripleWeights[i] !== tripleWeights[i-1] + 1) return null;
        }

        const expectedTripleCards = triples.length * 3;
        const remainingCards = cards.length - expectedTripleCards;

        if (remainingCards === 0) {
            // 纯飞机
            return {
                type: this.TYPES.TRIPLE_STRAIGHT,
                weight: tripleWeights[0],
                cards: cards,
                description: `飞机 ${triples.length}连`
            };
        } else if (remainingCards === triples.length) {
            // 飞机带单牌
            return {
                type: this.TYPES.TRIPLE_STRAIGHT_WITH_SINGLES,
                weight: tripleWeights[0],
                cards: cards,
                description: `飞机带单牌 ${triples.length}连`
            };
        } else if (remainingCards === triples.length * 2) {
            // 飞机带对子
            const nonTriples = Object.keys(valueCounts).filter(v => valueCounts[v] !== 3);
            if (nonTriples.every(v => valueCounts[v] === 2)) {
                return {
                    type: this.TYPES.TRIPLE_STRAIGHT_WITH_PAIRS,
                    weight: tripleWeights[0],
                    cards: cards,
                    description: `飞机带对子 ${triples.length}连`
                };
            }
        }

        return null;
    }

    /**
     * 检查四带二牌型
     */
    static checkFourWithTwo(cards, valueCounts) {
        const fours = Object.keys(valueCounts).filter(v => valueCounts[v] === 4);
        if (fours.length !== 1) return null;

        const fourWeight = Card.VALUES[fours[0]];
        const remainingCards = cards.length - 4;

        if (remainingCards === 2) {
            const nonFours = Object.keys(valueCounts).filter(v => valueCounts[v] !== 4);
            if (nonFours.length === 2 && nonFours.every(v => valueCounts[v] === 1)) {
                return {
                    type: this.TYPES.FOUR_WITH_TWO_SINGLES,
                    weight: fourWeight,
                    cards: cards,
                    description: `四带二单 ${Card.DISPLAY_NAMES[fours[0]]}`
                };
            } else if (nonFours.length === 1 && valueCounts[nonFours[0]] === 2) {
                return {
                    type: this.TYPES.FOUR_WITH_TWO_PAIRS,
                    weight: fourWeight,
                    cards: cards,
                    description: `四带二对 ${Card.DISPLAY_NAMES[fours[0]]}`
                };
            }
        }

        return null;
    }

    /**
     * 统计牌面值数量
     */
    static getValueCounts(cards) {
        const counts = {};
        cards.forEach(card => {
            const value = card.value;
            counts[value] = (counts[value] || 0) + 1;
        });
        return counts;
    }

    /**
     * 比较两个牌型的大小
     * @param {Object} pattern1 牌型1
     * @param {Object} pattern2 牌型2
     * @returns {number} 比较结果 (-1: 小于, 0: 不可比较, 1: 大于)
     */
    static compare(pattern1, pattern2) {
        if (!pattern1 || !pattern2) return 0;

        // 火箭最大
        if (pattern1.type === this.TYPES.ROCKET) return 1;
        if (pattern2.type === this.TYPES.ROCKET) return -1;

        // 炸弹大于非炸弹
        if (pattern1.type === this.TYPES.BOMB && pattern2.type !== this.TYPES.BOMB) return 1;
        if (pattern2.type === this.TYPES.BOMB && pattern1.type !== this.TYPES.BOMB) return -1;

        // 同类型牌型比较权重
        if (pattern1.type === pattern2.type) {
            if (pattern1.weight < pattern2.weight) return -1;
            if (pattern1.weight > pattern2.weight) return 1;
            return 0;
        }

        // 不同类型无法比较
        return 0;
    }
}
