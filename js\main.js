import Utils from './utils.js';
import Player from './player.js';
import { AIPlayer } from './ai.js';
import DouDiZhuGame from './game.js';
import GameUI from './ui.js';
import AudioManager from './audio.js';

/**
 * 斗地主游戏主程序
 */
class DouDiZhuApp {
    /**
     * 构造函数
     */
    constructor() {
        this.game = null;
        this.ui = null;
        this.audio = null;
        this.players = [];
        this.gameSettings = {
            aiDifficulty: 'medium',
            soundEnabled: true,
            musicEnabled: true
        };
        
        this.initialize();
    }

    /**
     * 初始化应用
     */
    initialize() {
        // 获取DOM元素
        this.canvas = document.getElementById('gameCanvas');
        this.startBtn = document.getElementById('startBtn');
        this.callLandlordBtn = document.getElementById('callLandlordBtn');
        this.passBtn = document.getElementById('passBtn');
        this.playCardsBtn = document.getElementById('playCardsBtn');
        this.skipBtn = document.getElementById('skipBtn');
        this.hintBtn = document.getElementById('hintBtn');
        this.restartBtn = document.getElementById('restartBtn');
        this.settingsBtn = document.getElementById('settingsBtn');
        this.closeSettingsBtn = document.getElementById('closeSettingsBtn');
        this.settingsPanel = document.getElementById('settingsPanel');
        this.statusText = document.getElementById('statusText');
        this.currentPlayer = document.getElementById('currentPlayer');
        this.playerScore = document.getElementById('playerScore');
        this.ai1Score = document.getElementById('ai1Score');
        this.ai2Score = document.getElementById('ai2Score');
        this.soundToggle = document.getElementById('soundToggle');
        this.aiDifficulty = document.getElementById('aiDifficulty');

        // 初始化组件
        this.initializeComponents();
        
        // 绑定事件
        this.bindEvents();
        
        // 加载设置
        this.loadSettings();
        
        console.log('斗地主游戏初始化完成');
    }

    /**
     * 初始化游戏组件
     */
    initializeComponents() {
        // 初始化UI
        this.ui = new GameUI(this.canvas);
        
        // 初始化音频
        this.audio = new AudioManager();
        
        // 创建玩家
        this.createPlayers();
        
        // 初始化游戏
        this.game = new DouDiZhuGame();
        this.ui.setGame(this.game);
        
        // 绑定游戏事件
        this.bindGameEvents();
    }

    /**
     * 创建玩家
     */
    createPlayers() {
        this.players = [
            new Player('player1', '玩家', Player.TYPES.HUMAN),
            new AIPlayer('ai1', '电脑1', this.gameSettings.aiDifficulty),
            new AIPlayer('ai2', '电脑2', this.gameSettings.aiDifficulty)
        ];
    }

    /**
     * 绑定DOM事件
     */
    bindEvents() {
        // 游戏按钮事件
        this.startBtn.addEventListener('click', () => this.startGame());
        this.callLandlordBtn.addEventListener('click', () => this.callLandlord(true));
        this.passBtn.addEventListener('click', () => this.callLandlord(false));
        this.playCardsBtn.addEventListener('click', () => this.playSelectedCards());
        this.skipBtn.addEventListener('click', () => this.skipTurn());
        this.hintBtn.addEventListener('click', () => this.showHint());
        this.restartBtn.addEventListener('click', () => this.restartGame());
        
        // 设置按钮事件
        this.settingsBtn.addEventListener('click', () => this.showSettings());
        this.closeSettingsBtn.addEventListener('click', () => this.hideSettings());
        
        // 设置变更事件
        this.soundToggle.addEventListener('change', () => this.updateAudioSettings());
        this.aiDifficulty.addEventListener('change', () => this.updateAISettings());
        
        // UI事件
        this.ui.on('cardSelectionChanged', (data) => this.onCardSelectionChanged(data));
        
        // 窗口大小变化事件
        window.addEventListener('resize', () => this.handleResize());
        
        // 用户交互恢复音频上下文
        document.addEventListener('click', () => this.audio.resumeAudioContext(), { once: true });
    }

    /**
     * 绑定游戏事件
     */
    bindGameEvents() {
        this.game.on('gameStarted', () => this.onGameStarted());
        this.game.on('cardsDealt', (data) => this.onCardsDealt(data));
        this.game.on('callingPhaseStarted', (data) => this.onCallingPhaseStarted(data));
        this.game.on('landlordCalled', (data) => this.onLandlordCalled(data));
        this.game.on('landlordFinalized', (data) => this.onLandlordFinalized(data));
        this.game.on('cardsPlayed', (data) => this.onCardsPlayed(data));
        this.game.on('playerPassed', (data) => this.onPlayerPassed(data));
        this.game.on('nextPlayer', (data) => this.onNextPlayer(data));
        this.game.on('gameEnded', (data) => this.onGameEnded(data));
    }

    /**
     * 开始游戏
     */
    startGame() {
        try {
            this.game.initialize(this.players);
            this.game.startGame();
            this.audio.playSound('button');
            this.audio.playBackgroundMusic();
        } catch (error) {
            console.error('开始游戏失败:', error);
            this.updateStatus('游戏启动失败: ' + error.message);
        }
    }

    /**
     * 重新开始游戏
     */
    restartGame() {
        this.audio.playSound('button');
        this.hideAllButtons();
        this.ui.clearSelection();
        this.startGame();
    }

    /**
     * 叫地主
     * @param {boolean} call 是否叫地主
     */
    callLandlord(call) {
        try {
            this.game.callLandlord('player1', call);
            this.audio.playSound('button');
        } catch (error) {
            console.error('叫地主失败:', error);
            this.updateStatus('操作失败: ' + error.message);
        }
    }

    /**
     * 出选中的牌
     */
    playSelectedCards() {
        const selectedCards = this.ui.getSelectedCards();
        
        if (selectedCards.length === 0) {
            this.updateStatus('请先选择要出的牌');
            return;
        }

        try {
            this.game.playCards('player1', selectedCards);
            this.ui.clearSelection();
            this.audio.playSound('play');
        } catch (error) {
            console.error('出牌失败:', error);
            this.updateStatus('出牌失败: ' + error.message);
        }
    }

    /**
     * 跳过回合
     */
    skipTurn() {
        try {
            this.game.pass('player1');
            this.audio.playSound('pass');
        } catch (error) {
            console.error('跳过失败:', error);
            this.updateStatus('操作失败: ' + error.message);
        }
    }

    /**
     * 显示提示
     */
    showHint() {
        if (!this.game || this.game.getCurrentPlayer().id !== 'player1') {
            return;
        }

        const player = this.game.getCurrentPlayer();
        const playablePatterns = player.getPlayablePatterns(this.game.lastPlayedPattern);
        
        if (playablePatterns.length > 0) {
            const hint = playablePatterns[0];
            this.updateStatus(`提示: 可以出 ${hint.description}`);
            
            // 高亮提示的牌
            this.ui.clearSelection();
            hint.cards.forEach(card => {
                const handCard = player.hand.find(c => c.equals(card));
                if (handCard) {
                    this.ui.toggleCardSelection(handCard);
                }
            });
        } else {
            this.updateStatus('没有可出的牌，建议过牌');
        }
        
        this.audio.playSound('button');
    }

    /**
     * 显示设置面板
     */
    showSettings() {
        this.settingsPanel.style.display = 'block';
        this.audio.playSound('button');
    }

    /**
     * 隐藏设置面板
     */
    hideSettings() {
        this.settingsPanel.style.display = 'none';
        this.audio.playSound('button');
    }

    /**
     * 更新音频设置
     */
    updateAudioSettings() {
        this.gameSettings.soundEnabled = this.soundToggle.checked;
        this.audio.setSoundEnabled(this.gameSettings.soundEnabled);
        this.saveSettings();
    }

    /**
     * 更新AI设置
     */
    updateAISettings() {
        this.gameSettings.aiDifficulty = this.aiDifficulty.value;
        
        // 更新AI玩家难度
        this.players.forEach(player => {
            if (player instanceof AIPlayer) {
                player.setDifficulty(this.gameSettings.aiDifficulty);
            }
        });
        
        this.saveSettings();
    }

    /**
     * 处理卡牌选择变化
     * @param {Object} data 选择数据
     */
    onCardSelectionChanged(data) {
        this.audio.playSound('select');
        
        // 更新出牌按钮状态
        const hasSelection = data.selectedCards.length > 0;
        this.playCardsBtn.disabled = !hasSelection;
    }

    /**
     * 处理窗口大小变化
     */
    handleResize() {
        const container = this.canvas.parentElement;
        const rect = container.getBoundingClientRect();
        this.ui.resize(rect.width, rect.height);
    }

    // 游戏事件处理器

    /**
     * 游戏开始事件
     */
    onGameStarted() {
        this.updateStatus('游戏开始，正在发牌...');
        this.hideAllButtons();
        this.updateScores();
    }

    /**
     * 发牌完成事件
     * @param {Object} data 发牌数据
     */
    onCardsDealt(data) {
        this.updateStatus('发牌完成');
        this.ui.updatePlayerPositions();
        this.ui.render();
        this.audio.playSound('deal');
    }

    /**
     * 叫地主阶段开始事件
     * @param {Object} data 叫地主数据
     */
    onCallingPhaseStarted(data) {
        this.updateStatus('开始叫地主');
        this.updateCurrentPlayer(data.currentPlayer);
        
        if (data.currentPlayer.id === 'player1') {
            this.showLandlordButtons();
        } else {
            this.hideLandlordButtons();
            // AI自动叫地主
            this.handleAILandlordCall(data.currentPlayer);
        }
    }

    /**
     * 叫地主事件
     * @param {Object} data 叫地主数据
     */
    onLandlordCalled(data) {
        const action = data.called ? '叫地主' : '不叫';
        this.updateStatus(`${data.player.name} ${action}`);
        
        if (data.called) {
            this.audio.playSound('landlord');
        } else {
            this.audio.playSound('pass');
        }
    }

    /**
     * 地主确定事件
     * @param {Object} data 地主数据
     */
    onLandlordFinalized(data) {
        this.updateStatus(`${data.landlord.name} 成为地主`);
        this.hideLandlordButtons();
        this.ui.updatePlayerPositions();
        this.ui.render();
        this.audio.playSound('landlord');
    }

    /**
     * 出牌事件
     * @param {Object} data 出牌数据
     */
    onCardsPlayed(data) {
        this.updateStatus(`${data.player.name} 出牌: ${data.pattern.description}`);
        this.ui.updatePlayerPositions();
        this.ui.render();
        
        // 播放特殊音效
        if (data.pattern.type === CardPattern.TYPES.BOMB) {
            this.audio.playSound('bomb');
        } else if (data.pattern.type === CardPattern.TYPES.ROCKET) {
            this.audio.playSound('rocket');
        } else {
            this.audio.playSound('play');
        }
    }

    /**
     * 玩家过牌事件
     * @param {Object} data 过牌数据
     */
    onPlayerPassed(data) {
        this.updateStatus(`${data.player.name} 过牌`);
        this.audio.playSound('pass');
    }

    /**
     * 下一个玩家事件
     * @param {Object} data 玩家数据
     */
    onNextPlayer(data) {
        this.updateCurrentPlayer(data.currentPlayer);
        
        if (data.currentPlayer.id === 'player1') {
            this.showPlayButtons();
        } else {
            this.hidePlayButtons();
            // AI自动出牌
            this.handleAIPlay(data.currentPlayer);
        }
    }

    /**
     * 游戏结束事件
     * @param {Object} data 结束数据
     */
    onGameEnded(data) {
        const winMessage = data.landlordWins ? '地主获胜!' : '农民获胜!';
        this.updateStatus(`游戏结束 - ${winMessage}`);
        
        this.updateScores();
        this.showRestartButton();
        
        // 播放胜负音效
        const humanPlayer = this.players[0];
        const humanWins = (data.landlordWins && humanPlayer.isLandlord()) || 
                         (!data.landlordWins && humanPlayer.isFarmer());
        
        this.audio.playSound(humanWins ? 'win' : 'lose');
    }

    // AI处理方法

    /**
     * 处理AI叫地主
     * @param {AIPlayer} aiPlayer AI玩家
     */
    async handleAILandlordCall(aiPlayer) {
        await Utils.delay(1000); // 模拟思考时间
        
        const shouldCall = await aiPlayer.decideCallLandlord(this.game.landlordCards, this.game.players);
        this.game.callLandlord(aiPlayer.id, shouldCall);
    }

    /**
     * 处理AI出牌
     * @param {AIPlayer} aiPlayer AI玩家
     */
    async handleAIPlay(aiPlayer) {
        await Utils.delay(1500); // 模拟思考时间
        
        const cards = await aiPlayer.decidePlayCards(this.game.lastPlayedPattern, this.game.players);
        
        if (cards) {
            this.game.playCards(aiPlayer.id, cards);
        } else {
            this.game.pass(aiPlayer.id);
        }
    }

    // UI更新方法

    /**
     * 更新状态文本
     * @param {string} text 状态文本
     */
    updateStatus(text) {
        this.statusText.textContent = text;
    }

    /**
     * 更新当前玩家显示
     * @param {Player} player 当前玩家
     */
    updateCurrentPlayer(player) {
        this.currentPlayer.textContent = `当前玩家: ${player.name}`;
    }

    /**
     * 更新分数显示
     */
    updateScores() {
        this.playerScore.textContent = this.players[0].totalScore;
        this.ai1Score.textContent = this.players[1].totalScore;
        this.ai2Score.textContent = this.players[2].totalScore;
    }

    /**
     * 显示叫地主按钮
     */
    showLandlordButtons() {
        this.callLandlordBtn.style.display = 'inline-block';
        this.passBtn.style.display = 'inline-block';
    }

    /**
     * 隐藏叫地主按钮
     */
    hideLandlordButtons() {
        this.callLandlordBtn.style.display = 'none';
        this.passBtn.style.display = 'none';
    }

    /**
     * 显示出牌按钮
     */
    showPlayButtons() {
        this.playCardsBtn.style.display = 'inline-block';
        this.skipBtn.style.display = 'inline-block';
        this.hintBtn.style.display = 'inline-block';
    }

    /**
     * 隐藏出牌按钮
     */
    hidePlayButtons() {
        this.playCardsBtn.style.display = 'none';
        this.skipBtn.style.display = 'none';
        this.hintBtn.style.display = 'none';
    }

    /**
     * 显示重新开始按钮
     */
    showRestartButton() {
        this.restartBtn.style.display = 'inline-block';
    }

    /**
     * 隐藏所有游戏按钮
     */
    hideAllButtons() {
        this.hideLandlordButtons();
        this.hidePlayButtons();
        this.restartBtn.style.display = 'none';
    }

    /**
     * 保存设置
     */
    saveSettings() {
        Utils.storage.set('gameSettings', this.gameSettings);
    }

    /**
     * 加载设置
     */
    loadSettings() {
        const saved = Utils.storage.get('gameSettings', {});
        this.gameSettings = { ...this.gameSettings, ...saved };
        
        // 应用设置到UI
        this.soundToggle.checked = this.gameSettings.soundEnabled;
        this.aiDifficulty.value = this.gameSettings.aiDifficulty;
        
        // 应用音频设置
        this.audio.setSoundEnabled(this.gameSettings.soundEnabled);
    }
}

// 应用启动
document.addEventListener('DOMContentLoaded', () => {
    window.douDiZhuApp = new DouDiZhuApp();
});
