/* 卡牌动画效果 */
@keyframes dealCard {
    0% {
        transform: translateX(-100px) translateY(-100px) rotate(-90deg);
        opacity: 0;
    }
    100% {
        transform: translateX(0) translateY(0) rotate(0deg);
        opacity: 1;
    }
}

@keyframes playCard {
    0% {
        transform: translateY(0) scale(1);
    }
    50% {
        transform: translateY(-30px) scale(1.1);
    }
    100% {
        transform: translateY(-60px) scale(1);
        opacity: 0;
    }
}

@keyframes cardFlip {
    0% {
        transform: rotateY(0deg);
    }
    50% {
        transform: rotateY(90deg);
    }
    100% {
        transform: rotateY(0deg);
    }
}

@keyframes cardHover {
    0% {
        transform: translateY(0);
    }
    100% {
        transform: translateY(-10px);
    }
}

@keyframes cardSelect {
    0% {
        transform: translateY(0) scale(1);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    }
    100% {
        transform: translateY(-15px) scale(1.05);
        box-shadow: 0 6px 16px rgba(255, 215, 0, 0.6);
    }
}

/* 游戏状态动画 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-20px);
    }
}

@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-10px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(10px);
    }
}

@keyframes glow {
    0% {
        box-shadow: 0 0 5px rgba(255, 215, 0, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(255, 215, 0, 0.8), 0 0 30px rgba(255, 215, 0, 0.6);
    }
    100% {
        box-shadow: 0 0 5px rgba(255, 215, 0, 0.5);
    }
}

/* 特效动画类 */
.animate-deal {
    animation: dealCard 0.5s ease-out forwards;
}

.animate-play {
    animation: playCard 0.8s ease-out forwards;
}

.animate-flip {
    animation: cardFlip 0.6s ease-in-out;
}

.animate-select {
    animation: cardSelect 0.3s ease-out forwards;
}

.animate-fade-in {
    animation: fadeIn 0.5s ease-out;
}

.animate-fade-out {
    animation: fadeOut 0.5s ease-out;
}

.animate-slide-left {
    animation: slideInLeft 0.5s ease-out;
}

.animate-slide-right {
    animation: slideInRight 0.5s ease-out;
}

.animate-slide-up {
    animation: slideInUp 0.5s ease-out;
}

.animate-bounce {
    animation: bounce 1s ease-out;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

.animate-shake {
    animation: shake 0.5s ease-in-out;
}

.animate-glow {
    animation: glow 2s infinite;
}

/* 按钮动画效果 */
@keyframes buttonPress {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(0.95);
    }
    100% {
        transform: scale(1);
    }
}

.game-btn.pressed {
    animation: buttonPress 0.2s ease-out;
}

/* 分数变化动画 */
@keyframes scoreChange {
    0% {
        transform: scale(1);
        color: inherit;
    }
    50% {
        transform: scale(1.2);
        color: #FFD700;
    }
    100% {
        transform: scale(1);
        color: inherit;
    }
}

.score-change {
    animation: scoreChange 0.6s ease-out;
}

/* 胜利/失败动画 */
@keyframes victory {
    0% {
        transform: scale(1) rotate(0deg);
    }
    25% {
        transform: scale(1.1) rotate(-5deg);
    }
    50% {
        transform: scale(1.2) rotate(5deg);
    }
    75% {
        transform: scale(1.1) rotate(-5deg);
    }
    100% {
        transform: scale(1) rotate(0deg);
    }
}

@keyframes defeat {
    0% {
        transform: translateX(0);
    }
    25% {
        transform: translateX(-5px);
    }
    50% {
        transform: translateX(5px);
    }
    75% {
        transform: translateX(-5px);
    }
    100% {
        transform: translateX(0);
    }
}

.victory-animation {
    animation: victory 1s ease-in-out;
}

.defeat-animation {
    animation: defeat 0.5s ease-in-out;
}

/* 加载动画 */
@keyframes loadingDots {
    0%, 20% {
        color: rgba(255, 255, 255, 0);
        text-shadow: 0.25em 0 0 rgba(255, 255, 255, 0),
                     0.5em 0 0 rgba(255, 255, 255, 0);
    }
    40% {
        color: white;
        text-shadow: 0.25em 0 0 rgba(255, 255, 255, 0),
                     0.5em 0 0 rgba(255, 255, 255, 0);
    }
    60% {
        text-shadow: 0.25em 0 0 white,
                     0.5em 0 0 rgba(255, 255, 255, 0);
    }
    80%, 100% {
        text-shadow: 0.25em 0 0 white,
                     0.5em 0 0 white;
    }
}

.loading-dots::after {
    content: "...";
    animation: loadingDots 1.5s infinite;
}

/* 过渡效果 */
.transition-all {
    transition: all 0.3s ease;
}

.transition-transform {
    transition: transform 0.3s ease;
}

.transition-opacity {
    transition: opacity 0.3s ease;
}

.transition-color {
    transition: color 0.3s ease;
}
