# HTML5 斗地主游戏

一个完整的基于HTML5的斗地主游戏，支持单机模式和AI对手。

## 功能特性

### 🎮 游戏功能
- ✅ 完整的斗地主游戏规则
- ✅ 智能AI对手（3个难度等级）
- ✅ 完整的牌型识别系统
- ✅ 动画效果和音效
- ✅ 响应式设计，支持移动端
- ✅ 游戏统计和分数系统

### 🎯 牌型支持
- 单牌、对子、三张
- 三带一、三带二
- 顺子、连对、飞机
- 四带二（单/对）
- 炸弹、火箭（双王）

### 🤖 AI系统
- **简单AI**: 随机策略，适合新手
- **中等AI**: 平衡策略，有一定挑战性
- **困难AI**: 智能策略，具有挑战性

### 🎨 界面特性
- 精美的卡牌渲染
- 流畅的动画效果
- 直观的用户界面
- 音效和背景音乐

## 技术架构

### 前端技术
- **HTML5 Canvas**: 游戏渲染
- **JavaScript ES6**: 游戏逻辑
- **CSS3**: 样式和动画
- **Web Audio API**: 音效系统

### 核心模块
```
js/
├── utils.js          # 工具函数
├── card.js           # 卡牌类
├── deck.js           # 牌组和牌型识别
├── player.js         # 玩家类
├── ai.js             # AI系统
├── game.js           # 游戏核心逻辑
├── cardRenderer.js   # 卡牌渲染器
├── ui.js             # 用户界面
├── audio.js          # 音效管理
└── main.js           # 主程序
```

## 快速开始

### 1. 运行游戏
直接在浏览器中打开 `index.html` 文件即可开始游戏。

### 2. 游戏操作
- **开始游戏**: 点击"开始游戏"按钮
- **叫地主**: 在叫地主阶段选择"叫地主"或"不叫"
- **选择卡牌**: 点击卡牌进行选择/取消选择
- **出牌**: 选择卡牌后点击"出牌"
- **过牌**: 点击"不出"跳过当前回合
- **提示**: 点击"提示"获取出牌建议

### 3. 设置选项
- **音效开关**: 控制游戏音效
- **AI难度**: 选择AI对手难度等级

## 开发和测试

### 测试页面
打开 `test.html` 可以运行各种功能测试：
- 基础组件测试
- 卡牌系统测试
- 牌型识别测试
- 卡牌渲染测试
- AI系统测试
- 游戏逻辑测试

### 本地开发
由于使用了ES6模块和Canvas，建议使用本地服务器运行：

```bash
# 使用Python
python -m http.server 8000

# 使用Node.js
npx http-server

# 使用PHP
php -S localhost:8000
```

然后访问 `http://localhost:8000`

## 游戏规则

### 基本规则
1. 三人游戏，一人做地主，两人做农民
2. 地主先出牌，然后按逆时针方向轮流出牌
3. 玩家可以选择出牌或过牌
4. 后出的牌必须比前一手牌大
5. 最先出完手牌的一方获胜

### 牌型大小
- **单牌**: 3 < 4 < 5 < 6 < 7 < 8 < 9 < 10 < J < Q < K < A < 2 < 小王 < 大王
- **对子**: 按牌面值比较
- **三张**: 按牌面值比较
- **顺子**: 最小5张，最大到A，不能包含2和王
- **炸弹**: 四张相同牌面值的牌，可以压任何非炸弹牌型
- **火箭**: 双王，最大的牌型

### 特殊规则
- 炸弹可以压任何非炸弹牌型
- 火箭可以压任何牌型
- 大炸弹可以压小炸弹

## 项目结构

```
斗地主游戏/
├── index.html          # 主游戏页面
├── test.html           # 测试页面
├── README.md           # 项目说明
├── css/                # 样式文件
│   ├── style.css       # 主样式
│   └── animations.css  # 动画样式
├── js/                 # JavaScript文件
│   ├── utils.js        # 工具函数
│   ├── card.js         # 卡牌系统
│   ├── deck.js         # 牌组系统
│   ├── player.js       # 玩家系统
│   ├── ai.js           # AI系统
│   ├── game.js         # 游戏逻辑
│   ├── cardRenderer.js # 卡牌渲染
│   ├── ui.js           # 用户界面
│   ├── audio.js        # 音效系统
│   └── main.js         # 主程序
└── assets/             # 资源文件
    └── README.md       # 资源说明
```

## 浏览器兼容性

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 11+
- ✅ Edge 79+
- ⚠️ IE 不支持

## 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。

## 贡献

欢迎提交 Issue 和 Pull Request！

### 开发指南
1. Fork 本仓库
2. 创建功能分支
3. 提交更改
4. 运行测试
5. 创建 Pull Request

## 更新日志

### v1.0.0 (2025-01-31)
- ✅ 完整的斗地主游戏实现
- ✅ 智能AI对手系统
- ✅ 完整的牌型识别
- ✅ 音效和动画系统
- ✅ 响应式用户界面
- ✅ 游戏统计功能

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交 GitHub Issue
- 发送邮件至项目维护者

---

**享受游戏！** 🎉
