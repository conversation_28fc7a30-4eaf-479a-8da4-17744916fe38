/**
 * 卡牌渲染器 - 使用Canvas绘制卡牌图像
 */
export default class CardRenderer {
    /**
     * 构造函数
     */
    constructor() {
        this.cardCache = new Map();
        this.cardSize = { width: 60, height: 84 };
        this.cornerRadius = 8;
        
        // 花色符号
        this.suitSymbols = {
            spades: '♠',
            hearts: '♥',
            diamonds: '♦',
            clubs: '♣'
        };
        
        // 花色颜色
        this.suitColors = {
            spades: '#000000',
            hearts: '#FF0000',
            diamonds: '#FF0000',
            clubs: '#000000'
        };
        
        // 牌面值显示
        this.valueDisplays = {
            3: '3', 4: '4', 5: '5', 6: '6', 7: '7', 8: '8', 9: '9', 10: '10',
            J: 'J', Q: 'Q', K: 'K', A: 'A', 2: '2',
            JOKER_SMALL: '小王', JOKER_BIG: '大王'
        };
    }

    /**
     * 渲染卡牌
     * @param {Card} card 卡牌对象
     * @param {boolean} showFace 是否显示正面
     * @param {Object} options 渲染选项
     * @returns {HTMLCanvasElement} 渲染后的Canvas元素
     */
    renderCard(card, showFace = true, options = {}) {
        const cacheKey = this.getCacheKey(card, showFace, options);
        
        if (this.cardCache.has(cacheKey)) {
            return this.cardCache.get(cacheKey);
        }

        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        const width = options.width || this.cardSize.width;
        const height = options.height || this.cardSize.height;
        
        canvas.width = width;
        canvas.height = height;

        if (showFace) {
            this.drawCardFace(ctx, card, width, height, options);
        } else {
            this.drawCardBack(ctx, width, height, options);
        }

        this.cardCache.set(cacheKey, canvas);
        return canvas;
    }

    /**
     * 绘制卡牌正面
     * @param {CanvasRenderingContext2D} ctx Canvas上下文
     * @param {Card} card 卡牌对象
     * @param {number} width 宽度
     * @param {number} height 高度
     * @param {Object} options 选项
     */
    drawCardFace(ctx, card, width, height, options) {
        // 绘制卡牌背景
        this.drawCardBackground(ctx, width, height, '#FFFFFF', options);
        
        if (card.isJoker()) {
            this.drawJoker(ctx, card, width, height);
        } else {
            this.drawRegularCard(ctx, card, width, height);
        }
    }

    /**
     * 绘制卡牌背面
     * @param {CanvasRenderingContext2D} ctx Canvas上下文
     * @param {number} width 宽度
     * @param {number} height 高度
     * @param {Object} options 选项
     */
    drawCardBack(ctx, width, height, options) {
        // 绘制背景渐变
        const gradient = ctx.createLinearGradient(0, 0, width, height);
        gradient.addColorStop(0, '#1e3c72');
        gradient.addColorStop(0.5, '#2a5298');
        gradient.addColorStop(1, '#1e3c72');
        
        this.drawCardBackground(ctx, width, height, gradient, options);
        
        // 绘制装饰图案
        this.drawBackPattern(ctx, width, height);
    }

    /**
     * 绘制卡牌背景
     * @param {CanvasRenderingContext2D} ctx Canvas上下文
     * @param {number} width 宽度
     * @param {number} height 高度
     * @param {string|CanvasGradient} fillStyle 填充样式
     * @param {Object} options 选项
     */
    drawCardBackground(ctx, width, height, fillStyle, options) {
        ctx.fillStyle = fillStyle;

        // 绘制圆角矩形（兼容性处理）
        this.drawRoundedRect(ctx, 0, 0, width, height, this.cornerRadius);
        ctx.fill();

        // 绘制边框
        const borderColor = options.selected ? '#FFD700' :
                           options.hovered ? '#87CEEB' : '#333333';
        const borderWidth = options.selected ? 3 : 2;

        ctx.strokeStyle = borderColor;
        ctx.lineWidth = borderWidth;
        ctx.stroke();
    }

    /**
     * 绘制圆角矩形（兼容性方法）
     * @param {CanvasRenderingContext2D} ctx Canvas上下文
     * @param {number} x X坐标
     * @param {number} y Y坐标
     * @param {number} width 宽度
     * @param {number} height 高度
     * @param {number} radius 圆角半径
     */
    drawRoundedRect(ctx, x, y, width, height, radius) {
        if (typeof ctx.roundRect === 'function') {
            // 使用原生roundRect方法
            ctx.beginPath();
            ctx.roundRect(x, y, width, height, radius);
        } else {
            // 手动绘制圆角矩形
            ctx.beginPath();
            ctx.moveTo(x + radius, y);
            ctx.lineTo(x + width - radius, y);
            ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
            ctx.lineTo(x + width, y + height - radius);
            ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
            ctx.lineTo(x + radius, y + height);
            ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
            ctx.lineTo(x, y + radius);
            ctx.quadraticCurveTo(x, y, x + radius, y);
            ctx.closePath();
        }
    }

    /**
     * 绘制普通卡牌
     * @param {CanvasRenderingContext2D} ctx Canvas上下文
     * @param {Card} card 卡牌对象
     * @param {number} width 宽度
     * @param {number} height 高度
     */
    drawRegularCard(ctx, card, width, height) {
        const suit = card.suit;
        const value = card.value;
        const color = this.suitColors[suit];
        const symbol = this.suitSymbols[suit];
        const displayValue = this.valueDisplays[value];

        ctx.fillStyle = color;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';

        // 绘制中央花色符号
        ctx.font = `${Math.floor(height * 0.3)}px Arial`;
        ctx.fillText(symbol, width / 2, height / 2 - 5);

        // 绘制中央数值
        ctx.font = `bold ${Math.floor(height * 0.2)}px Arial`;
        ctx.fillText(displayValue, width / 2, height / 2 + 15);

        // 绘制左上角标记
        ctx.font = `${Math.floor(height * 0.12)}px Arial`;
        ctx.textAlign = 'left';
        ctx.textBaseline = 'top';
        ctx.fillText(displayValue, 3, 3);
        ctx.fillText(symbol, 3, 15);

        // 绘制右下角标记（旋转180度）
        ctx.save();
        ctx.translate(width - 3, height - 3);
        ctx.rotate(Math.PI);
        ctx.textAlign = 'left';
        ctx.textBaseline = 'top';
        ctx.fillText(displayValue, 0, 0);
        ctx.fillText(symbol, 0, 12);
        ctx.restore();

        // 根据数值绘制花色图案
        this.drawSuitPattern(ctx, card, width, height);
    }

    /**
     * 绘制花色图案
     * @param {CanvasRenderingContext2D} ctx Canvas上下文
     * @param {Card} card 卡牌对象
     * @param {number} width 宽度
     * @param {number} height 高度
     */
    drawSuitPattern(ctx, card, width, height) {
        const value = card.value;
        const symbol = this.suitSymbols[card.suit];
        const color = this.suitColors[card.suit];
        
        ctx.fillStyle = color;
        ctx.font = `${Math.floor(height * 0.15)}px Arial`;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';

        const centerX = width / 2;
        const centerY = height / 2;
        const spacing = height * 0.12;

        // 根据牌面值绘制不同数量的花色符号
        switch (value) {
            case 'A':
                // A只在中央绘制一个大符号（已在drawRegularCard中绘制）
                break;
            case 2:
                ctx.fillText(symbol, centerX, centerY - spacing * 1.5);
                ctx.fillText(symbol, centerX, centerY + spacing * 1.5);
                break;
            case 3:
                ctx.fillText(symbol, centerX, centerY - spacing * 1.5);
                ctx.fillText(symbol, centerX, centerY);
                ctx.fillText(symbol, centerX, centerY + spacing * 1.5);
                break;
            case 4:
                ctx.fillText(symbol, centerX - spacing, centerY - spacing);
                ctx.fillText(symbol, centerX + spacing, centerY - spacing);
                ctx.fillText(symbol, centerX - spacing, centerY + spacing);
                ctx.fillText(symbol, centerX + spacing, centerY + spacing);
                break;
            case 5:
                ctx.fillText(symbol, centerX - spacing, centerY - spacing);
                ctx.fillText(symbol, centerX + spacing, centerY - spacing);
                ctx.fillText(symbol, centerX, centerY);
                ctx.fillText(symbol, centerX - spacing, centerY + spacing);
                ctx.fillText(symbol, centerX + spacing, centerY + spacing);
                break;
            case 6:
                ctx.fillText(symbol, centerX - spacing, centerY - spacing * 1.2);
                ctx.fillText(symbol, centerX + spacing, centerY - spacing * 1.2);
                ctx.fillText(symbol, centerX - spacing, centerY);
                ctx.fillText(symbol, centerX + spacing, centerY);
                ctx.fillText(symbol, centerX - spacing, centerY + spacing * 1.2);
                ctx.fillText(symbol, centerX + spacing, centerY + spacing * 1.2);
                break;
            case 7:
                ctx.fillText(symbol, centerX - spacing, centerY - spacing * 1.5);
                ctx.fillText(symbol, centerX + spacing, centerY - spacing * 1.5);
                ctx.fillText(symbol, centerX, centerY - spacing * 0.7);
                ctx.fillText(symbol, centerX - spacing, centerY);
                ctx.fillText(symbol, centerX + spacing, centerY);
                ctx.fillText(symbol, centerX - spacing, centerY + spacing * 1.5);
                ctx.fillText(symbol, centerX + spacing, centerY + spacing * 1.5);
                break;
            case 8:
                ctx.fillText(symbol, centerX - spacing, centerY - spacing * 1.5);
                ctx.fillText(symbol, centerX + spacing, centerY - spacing * 1.5);
                ctx.fillText(symbol, centerX, centerY - spacing * 0.7);
                ctx.fillText(symbol, centerX - spacing, centerY);
                ctx.fillText(symbol, centerX + spacing, centerY);
                ctx.fillText(symbol, centerX, centerY + spacing * 0.7);
                ctx.fillText(symbol, centerX - spacing, centerY + spacing * 1.5);
                ctx.fillText(symbol, centerX + spacing, centerY + spacing * 1.5);
                break;
            case 9:
                // 3x3网格，去掉中央
                for (let row = 0; row < 3; row++) {
                    for (let col = 0; col < 3; col++) {
                        if (row === 1 && col === 1) continue; // 跳过中央
                        const x = centerX + (col - 1) * spacing;
                        const y = centerY + (row - 1) * spacing * 1.2;
                        ctx.fillText(symbol, x, y);
                    }
                }
                break;
            case 10:
                // 3x3网格，去掉中央，加上顶部和底部中央
                for (let row = 0; row < 3; row++) {
                    for (let col = 0; col < 3; col++) {
                        if (row === 1 && col === 1) continue; // 跳过中央
                        const x = centerX + (col - 1) * spacing;
                        const y = centerY + (row - 1) * spacing * 1.2;
                        ctx.fillText(symbol, x, y);
                    }
                }
                ctx.fillText(symbol, centerX, centerY - spacing * 2);
                ctx.fillText(symbol, centerX, centerY + spacing * 2);
                break;
            case 'J':
            case 'Q':
            case 'K':
                // 人头牌绘制简化的人物图案
                this.drawFaceCard(ctx, card, width, height);
                break;
        }
    }

    /**
     * 绘制人头牌
     * @param {CanvasRenderingContext2D} ctx Canvas上下文
     * @param {Card} card 卡牌对象
     * @param {number} width 宽度
     * @param {number} height 高度
     */
    drawFaceCard(ctx, card, width, height) {
        const centerX = width / 2;
        const centerY = height / 2;
        const color = this.suitColors[card.suit];
        
        ctx.fillStyle = color;
        ctx.strokeStyle = color;
        ctx.lineWidth = 2;

        // 绘制简化的人物轮廓
        ctx.beginPath();
        // 头部
        ctx.arc(centerX, centerY - 10, 8, 0, Math.PI * 2);
        ctx.fill();
        
        // 身体
        ctx.beginPath();
        ctx.rect(centerX - 6, centerY, 12, 20);
        ctx.fill();
        
        // 根据牌面值添加不同的装饰
        ctx.font = `bold ${Math.floor(height * 0.15)}px Arial`;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(this.valueDisplays[card.value], centerX, centerY + 30);
    }

    /**
     * 绘制王牌
     * @param {CanvasRenderingContext2D} ctx Canvas上下文
     * @param {Card} card 卡牌对象
     * @param {number} width 宽度
     * @param {number} height 高度
     */
    drawJoker(ctx, card, width, height) {
        const centerX = width / 2;
        const centerY = height / 2;
        const isSmallJoker = card.value === 'JOKER_SMALL';
        
        // 设置颜色
        ctx.fillStyle = isSmallJoker ? '#FF0000' : '#000000';
        
        // 绘制王牌文字
        ctx.font = `bold ${Math.floor(height * 0.2)}px Arial`;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(this.valueDisplays[card.value], centerX, centerY);
        
        // 绘制装饰图案
        ctx.beginPath();
        ctx.arc(centerX, centerY - 20, 5, 0, Math.PI * 2);
        ctx.fill();
        
        ctx.beginPath();
        ctx.arc(centerX - 10, centerY + 15, 3, 0, Math.PI * 2);
        ctx.arc(centerX + 10, centerY + 15, 3, 0, Math.PI * 2);
        ctx.fill();
    }

    /**
     * 绘制卡牌背面图案
     * @param {CanvasRenderingContext2D} ctx Canvas上下文
     * @param {number} width 宽度
     * @param {number} height 高度
     */
    drawBackPattern(ctx, width, height) {
        ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
        ctx.lineWidth = 1;
        
        // 绘制对角线图案
        const spacing = 8;
        for (let i = -height; i < width + height; i += spacing) {
            ctx.beginPath();
            ctx.moveTo(i, 0);
            ctx.lineTo(i + height, height);
            ctx.stroke();
        }
        
        // 绘制中央装饰
        const centerX = width / 2;
        const centerY = height / 2;
        
        ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
        ctx.beginPath();
        ctx.arc(centerX, centerY, Math.min(width, height) * 0.15, 0, Math.PI * 2);
        ctx.fill();
    }

    /**
     * 生成缓存键
     * @param {Card} card 卡牌对象
     * @param {boolean} showFace 是否显示正面
     * @param {Object} options 选项
     * @returns {string} 缓存键
     */
    getCacheKey(card, showFace, options) {
        const optionsStr = JSON.stringify({
            width: options.width || this.cardSize.width,
            height: options.height || this.cardSize.height,
            selected: options.selected || false,
            hovered: options.hovered || false
        });
        
        return `${card.id}_${showFace}_${optionsStr}`;
    }

    /**
     * 清空缓存
     */
    clearCache() {
        this.cardCache.clear();
    }

    /**
     * 设置卡牌尺寸
     * @param {number} width 宽度
     * @param {number} height 高度
     */
    setCardSize(width, height) {
        this.cardSize.width = width;
        this.cardSize.height = height;
        this.clearCache(); // 清空缓存以使用新尺寸
    }
}
