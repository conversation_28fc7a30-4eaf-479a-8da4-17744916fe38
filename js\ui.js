import Utils from './utils.js';
import CardRenderer from './cardRenderer.js';

/**
 * 游戏用户界面管理器
 */
export default class GameUI extends Utils.EventEmitter {
    /**
     * 构造函数
     * @param {HTMLCanvasElement} canvas Canvas元素
     */
    constructor(canvas) {
        super();
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.width = canvas.width;
        this.height = canvas.height;

        // UI状态
        this.selectedCards = [];
        this.hoveredCard = null;
        this.animatingCards = [];

        // 玩家位置配置
        this.playerPositions = {
            0: { x: this.width / 2, y: this.height - 120, name: '玩家' },      // 底部（人类玩家）
            1: { x: 100, y: this.height / 2, name: '电脑1' },                 // 左侧
            2: { x: this.width - 100, y: this.height / 2, name: '电脑2' }     // 右侧
        };

        // 卡牌尺寸
        this.cardSize = { width: 60, height: 84 };
        this.cardSpacing = 20;

        // 初始化卡牌渲染器
        this.cardRenderer = new CardRenderer();
        this.cardRenderer.setCardSize(this.cardSize.width, this.cardSize.height);

        // 绑定事件
        this.bindEvents();

        // 初始化
        this.clear();
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        this.canvas.addEventListener('click', this.handleClick.bind(this));
        this.canvas.addEventListener('mousemove', this.handleMouseMove.bind(this));
        this.canvas.addEventListener('mouseout', this.handleMouseOut.bind(this));
    }

    /**
     * 处理点击事件
     * @param {MouseEvent} event 鼠标事件
     */
    handleClick(event) {
        const rect = this.canvas.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;
        
        const clickedCard = this.getCardAtPosition(x, y);
        if (clickedCard) {
            this.toggleCardSelection(clickedCard);
        }
    }

    /**
     * 处理鼠标移动事件
     * @param {MouseEvent} event 鼠标事件
     */
    handleMouseMove(event) {
        const rect = this.canvas.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;
        
        const hoveredCard = this.getCardAtPosition(x, y);
        if (hoveredCard !== this.hoveredCard) {
            this.hoveredCard = hoveredCard;
            this.render();
        }
    }

    /**
     * 处理鼠标离开事件
     */
    handleMouseOut() {
        if (this.hoveredCard) {
            this.hoveredCard = null;
            this.render();
        }
    }

    /**
     * 获取指定位置的卡牌
     * @param {number} x X坐标
     * @param {number} y Y坐标
     * @returns {Card|null} 卡牌对象或null
     */
    getCardAtPosition(x, y) {
        // 只检查人类玩家的卡牌（底部）
        if (!this.game || !this.game.players[0]) return null;
        
        const player = this.game.players[0];
        const cards = player.hand;
        
        // 从右到左检查卡牌（因为卡牌可能重叠）
        for (let i = cards.length - 1; i >= 0; i--) {
            const card = cards[i];
            const cardX = card.position.x;
            const cardY = card.position.y;
            
            if (x >= cardX && x <= cardX + this.cardSize.width &&
                y >= cardY && y <= cardY + this.cardSize.height) {
                return card;
            }
        }
        
        return null;
    }

    /**
     * 切换卡牌选中状态
     * @param {Card} card 卡牌对象
     */
    toggleCardSelection(card) {
        const index = this.selectedCards.findIndex(c => c.equals(card));
        
        if (index >= 0) {
            // 取消选中
            this.selectedCards.splice(index, 1);
            card.setSelected(false);
        } else {
            // 选中
            this.selectedCards.push(card);
            card.setSelected(true);
        }
        
        this.emit('cardSelectionChanged', {
            selectedCards: this.selectedCards,
            card: card,
            selected: card.selected
        });
        
        this.render();
    }

    /**
     * 清空选中的卡牌
     */
    clearSelection() {
        this.selectedCards.forEach(card => card.setSelected(false));
        this.selectedCards = [];
        this.render();
    }

    /**
     * 获取选中的卡牌
     * @returns {Card[]} 选中的卡牌数组
     */
    getSelectedCards() {
        return [...this.selectedCards];
    }

    /**
     * 设置游戏实例
     * @param {DouDiZhuGame} game 游戏实例
     */
    setGame(game) {
        this.game = game;
        this.updatePlayerPositions();
    }

    /**
     * 更新玩家卡牌位置
     */
    updatePlayerPositions() {
        if (!this.game) return;
        
        this.game.players.forEach((player, index) => {
            this.updatePlayerCardPositions(player, index);
        });
    }

    /**
     * 更新指定玩家的卡牌位置
     * @param {Player} player 玩家对象
     * @param {number} playerIndex 玩家索引
     */
    updatePlayerCardPositions(player, playerIndex) {
        const position = this.playerPositions[playerIndex];
        const cards = player.hand;
        
        if (cards.length === 0) return;
        
        const totalWidth = (cards.length - 1) * this.cardSpacing + this.cardSize.width;
        let startX = position.x - totalWidth / 2;
        
        // 确保卡牌不超出画布边界
        startX = Math.max(0, Math.min(startX, this.width - totalWidth));
        
        cards.forEach((card, cardIndex) => {
            const x = startX + cardIndex * this.cardSpacing;
            let y = position.y;
            
            // 人类玩家的选中卡牌上移
            if (playerIndex === 0 && card.selected) {
                y -= 20;
            }
            
            card.setPosition(x, y);
        });
    }

    /**
     * 渲染游戏界面
     */
    render() {
        this.clear();
        
        if (!this.game) {
            this.drawWelcomeScreen();
            return;
        }
        
        // 绘制游戏桌面
        this.drawGameTable();
        
        // 绘制玩家信息
        this.drawPlayersInfo();
        
        // 绘制卡牌
        this.drawCards();
        
        // 绘制最后出的牌
        this.drawLastPlayedCards();
        
        // 绘制游戏状态信息
        this.drawGameStatus();
    }

    /**
     * 清空画布
     */
    clear() {
        this.ctx.clearRect(0, 0, this.width, this.height);
    }

    /**
     * 绘制欢迎界面
     */
    drawWelcomeScreen() {
        this.ctx.fillStyle = '#ffffff';
        this.ctx.font = 'bold 48px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText('斗地主', this.width / 2, this.height / 2 - 50);
        
        this.ctx.font = '24px Arial';
        this.ctx.fillText('点击"开始游戏"开始', this.width / 2, this.height / 2 + 20);
    }

    /**
     * 绘制游戏桌面
     */
    drawGameTable() {
        // 绘制桌面背景
        const gradient = this.ctx.createRadialGradient(
            this.width / 2, this.height / 2, 0,
            this.width / 2, this.height / 2, Math.max(this.width, this.height) / 2
        );
        gradient.addColorStop(0, '#2d5a27');
        gradient.addColorStop(1, '#1a3318');
        
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.width, this.height);
        
        // 绘制桌面边框
        this.ctx.strokeStyle = '#8B4513';
        this.ctx.lineWidth = 8;
        this.ctx.strokeRect(4, 4, this.width - 8, this.height - 8);
    }

    /**
     * 绘制玩家信息
     */
    drawPlayersInfo() {
        this.game.players.forEach((player, index) => {
            const position = this.playerPositions[index];
            this.drawPlayerInfo(player, position, index);
        });
    }

    /**
     * 绘制单个玩家信息
     * @param {Player} player 玩家对象
     * @param {Object} position 位置信息
     * @param {number} playerIndex 玩家索引
     */
    drawPlayerInfo(player, position, playerIndex) {
        const isCurrentPlayer = this.game.currentPlayerIndex === playerIndex;
        const isLandlord = player.isLandlord();
        
        // 设置文本样式
        this.ctx.font = 'bold 16px Arial';
        this.ctx.textAlign = 'center';
        
        // 玩家名称
        this.ctx.fillStyle = isCurrentPlayer ? '#FFD700' : '#FFFFFF';
        let nameY = position.y - 40;
        if (playerIndex === 1) nameY = position.y - 20; // 左侧玩家
        if (playerIndex === 2) nameY = position.y - 20; // 右侧玩家
        
        let displayName = player.name;
        if (isLandlord) displayName += ' (地主)';
        
        this.ctx.fillText(displayName, position.x, nameY);
        
        // 手牌数量
        this.ctx.font = '14px Arial';
        this.ctx.fillStyle = '#CCCCCC';
        this.ctx.fillText(`${player.getHandCount()}张`, position.x, nameY + 20);
        
        // 当前玩家指示器
        if (isCurrentPlayer) {
            this.ctx.fillStyle = '#FFD700';
            this.ctx.beginPath();
            this.ctx.arc(position.x, nameY - 25, 8, 0, Math.PI * 2);
            this.ctx.fill();
        }
    }

    /**
     * 绘制所有玩家的卡牌
     */
    drawCards() {
        this.game.players.forEach((player, index) => {
            this.drawPlayerCards(player, index);
        });
    }

    /**
     * 绘制指定玩家的卡牌
     * @param {Player} player 玩家对象
     * @param {number} playerIndex 玩家索引
     */
    drawPlayerCards(player, playerIndex) {
        player.hand.forEach(card => {
            if (playerIndex === 0) {
                // 人类玩家显示正面
                this.drawCard(card, true);
            } else {
                // AI玩家显示背面
                this.drawCard(card, false);
            }
        });
    }

    /**
     * 绘制单张卡牌
     * @param {Card} card 卡牌对象
     * @param {boolean} showFace 是否显示正面
     */
    drawCard(card, showFace = true) {
        const x = card.position.x;
        const y = card.position.y;
        const width = this.cardSize.width;
        const height = this.cardSize.height;

        // 保存上下文
        this.ctx.save();

        // 应用变换
        this.ctx.translate(x + width / 2, y + height / 2);
        this.ctx.rotate(Utils.degreesToRadians(card.rotation));
        this.ctx.scale(card.scale, card.scale);
        this.ctx.translate(-width / 2, -height / 2);

        // 使用卡牌渲染器生成卡牌图像
        const options = {
            width: width,
            height: height,
            selected: card.selected,
            hovered: card === this.hoveredCard
        };

        const cardCanvas = this.cardRenderer.renderCard(card, showFace, options);
        this.ctx.drawImage(cardCanvas, 0, 0);

        // 恢复上下文
        this.ctx.restore();
    }



    /**
     * 绘制最后出的牌
     */
    drawLastPlayedCards() {
        if (!this.game.lastPlayedPattern) return;
        
        const cards = this.game.lastPlayedPattern.cards;
        const centerX = this.width / 2;
        const centerY = this.height / 2;
        
        // 绘制背景
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
        this.ctx.fillRect(centerX - 100, centerY - 50, 200, 100);
        
        // 绘制卡牌
        const startX = centerX - (cards.length * 15) / 2;
        cards.forEach((card, index) => {
            const x = startX + index * 15;
            const y = centerY - 42;
            
            // 临时设置卡牌位置
            const originalPos = { ...card.position };
            card.setPosition(x, y);
            
            this.drawCard(card, true);
            
            // 恢复原位置
            card.setPosition(originalPos.x, originalPos.y);
        });
        
        // 绘制牌型描述
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.font = '14px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(
            this.game.lastPlayedPattern.description,
            centerX,
            centerY + 35
        );
    }

    /**
     * 绘制游戏状态信息
     */
    drawGameStatus() {
        // 绘制游戏分数
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.font = 'bold 18px Arial';
        this.ctx.textAlign = 'left';
        this.ctx.fillText(`游戏倍数: ${this.game.gameScore}`, 20, 30);
        
        if (this.game.bombCount > 0) {
            this.ctx.fillText(`炸弹: ${this.game.bombCount}`, 20, 55);
        }
        
        if (this.game.rocketCount > 0) {
            this.ctx.fillText(`火箭: ${this.game.rocketCount}`, 20, 80);
        }
    }

    /**
     * 播放卡牌动画
     * @param {Card} card 卡牌对象
     * @param {string} animationType 动画类型
     * @param {Object} options 动画选项
     */
    async playCardAnimation(card, animationType, options = {}) {
        const duration = options.duration || 500;
        const startTime = Date.now();
        
        return new Promise(resolve => {
            const animate = () => {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / duration, 1);
                
                switch (animationType) {
                    case 'deal':
                        this.animateDeal(card, progress, options);
                        break;
                    case 'play':
                        this.animatePlay(card, progress, options);
                        break;
                    case 'select':
                        this.animateSelect(card, progress, options);
                        break;
                }
                
                this.render();
                
                if (progress < 1) {
                    requestAnimationFrame(animate);
                } else {
                    resolve();
                }
            };
            
            animate();
        });
    }

    /**
     * 发牌动画
     * @param {Card} card 卡牌对象
     * @param {number} progress 动画进度 (0-1)
     * @param {Object} options 动画选项
     */
    animateDeal(card, progress, options) {
        const easeProgress = Utils.easeInOutQuad(progress);
        const startX = options.startX || -100;
        const startY = options.startY || -100;
        const targetX = options.targetX || card.position.x;
        const targetY = options.targetY || card.position.y;
        
        card.setPosition(
            Utils.lerp(startX, targetX, easeProgress),
            Utils.lerp(startY, targetY, easeProgress)
        );
        
        card.setRotation(Utils.lerp(-90, 0, easeProgress));
        card.setScale(Utils.lerp(0.5, 1, easeProgress));
    }

    /**
     * 出牌动画
     * @param {Card} card 卡牌对象
     * @param {number} progress 动画进度 (0-1)
     * @param {Object} options 动画选项
     */
    animatePlay(card, progress, options) {
        const easeProgress = Utils.easeInOutQuad(progress);
        const startY = options.startY || card.position.y;
        const targetY = startY - 60;
        
        card.setPosition(card.position.x, Utils.lerp(startY, targetY, easeProgress));
        card.setScale(Utils.lerp(1, 1.2, progress < 0.5 ? progress * 2 : (1 - progress) * 2));
        
        if (progress > 0.8) {
            card.setVisible(false);
        }
    }

    /**
     * 选中动画
     * @param {Card} card 卡牌对象
     * @param {number} progress 动画进度 (0-1)
     * @param {Object} options 动画选项
     */
    animateSelect(card, progress, options) {
        const easeProgress = Utils.easeInOutQuad(progress);
        const startY = options.startY || card.position.y;
        const targetY = startY - (card.selected ? 15 : -15);
        
        card.setPosition(card.position.x, Utils.lerp(startY, targetY, easeProgress));
    }

    /**
     * 调整画布大小
     * @param {number} width 新宽度
     * @param {number} height 新高度
     */
    resize(width, height) {
        this.canvas.width = width;
        this.canvas.height = height;
        this.width = width;
        this.height = height;
        
        // 重新计算玩家位置
        this.playerPositions = {
            0: { x: this.width / 2, y: this.height - 120, name: '玩家' },
            1: { x: 100, y: this.height / 2, name: '电脑1' },
            2: { x: this.width - 100, y: this.height / 2, name: '电脑2' }
        };
        
        this.updatePlayerPositions();
        this.render();
    }
}
