import Utils from './utils.js';
import { Deck, CardPattern } from './deck.js';
import Player from './player.js';

/**
 * 斗地主游戏核心逻辑类
 */
export default class DouDiZhuGame extends Utils.EventEmitter {
    // 游戏状态常量
    static STATES = {
        WAITING: 'waiting',           // 等待开始
        DEALING: 'dealing',           // 发牌中
        CALLING: 'calling',           // 叫地主阶段
        PLAYING: 'playing',           // 游戏进行中
        FINISHED: 'finished'          // 游戏结束
    };

    // 游戏配置
    static CONFIG = {
        HAND_SIZE: 17,               // 每人手牌数量
        LANDLORD_EXTRA_CARDS: 3,     // 地主额外卡牌数量
        BASE_SCORE: 1,               // 基础分数
        BOMB_MULTIPLIER: 2,          // 炸弹倍数
        ROCKET_MULTIPLIER: 4         // 火箭倍数
    };

    /**
     * 构造函数
     */
    constructor() {
        super();
        this.state = DouDiZhuGame.STATES.WAITING;
        this.players = [];
        this.deck = new Deck();
        this.landlordCards = []; // 地主牌
        this.currentPlayerIndex = 0;
        this.landlordIndex = -1;
        this.lastPlayedPattern = null;
        this.lastPlayerIndex = -1;
        this.passCount = 0;
        this.gameScore = DouDiZhuGame.CONFIG.BASE_SCORE;
        this.bombCount = 0;
        this.rocketCount = 0;
        this.gameHistory = [];
        this.turnHistory = [];
    }

    /**
     * 初始化游戏
     * @param {Player[]} players 玩家数组
     */
    initialize(players) {
        if (players.length !== 3) {
            throw new Error('斗地主需要3个玩家');
        }

        this.players = players;
        this.state = DouDiZhuGame.STATES.WAITING;
        this.resetGame();
        
        this.emit('gameInitialized', { players: this.players });
    }

    /**
     * 开始新游戏
     */
    startGame() {
        if (this.state !== DouDiZhuGame.STATES.WAITING) {
            throw new Error('游戏状态不正确，无法开始游戏');
        }

        this.resetGame();
        this.dealCards();
        this.startCallingPhase();
        
        this.emit('gameStarted');
    }

    /**
     * 重置游戏状态
     */
    resetGame() {
        this.deck.reset();
        this.landlordCards = [];
        this.currentPlayerIndex = 0;
        this.landlordIndex = -1;
        this.lastPlayedPattern = null;
        this.lastPlayerIndex = -1;
        this.passCount = 0;
        this.gameScore = DouDiZhuGame.CONFIG.BASE_SCORE;
        this.bombCount = 0;
        this.rocketCount = 0;
        this.turnHistory = [];

        // 重置玩家状态
        this.players.forEach(player => {
            player.clearHand();
            player.resetScore();
            player.setRole(null);
            player.setActive(false);
            player.resetLandlordCall();
        });
    }

    /**
     * 发牌
     */
    dealCards() {
        this.state = DouDiZhuGame.STATES.DEALING;
        
        // 发给每个玩家17张牌
        for (let i = 0; i < DouDiZhuGame.CONFIG.HAND_SIZE; i++) {
            for (let j = 0; j < this.players.length; j++) {
                const card = this.deck.dealCard();
                if (card) {
                    this.players[j].addCards(card);
                }
            }
        }

        // 剩余3张作为地主牌
        this.landlordCards = this.deck.dealCards(DouDiZhuGame.CONFIG.LANDLORD_EXTRA_CARDS);
        
        this.emit('cardsDealt', {
            players: this.players.map(p => ({ id: p.id, handCount: p.getHandCount() })),
            landlordCards: this.landlordCards
        });
    }

    /**
     * 开始叫地主阶段
     */
    startCallingPhase() {
        this.state = DouDiZhuGame.STATES.CALLING;
        this.currentPlayerIndex = Utils.randomInt(0, 2); // 随机选择起始玩家
        
        this.emit('callingPhaseStarted', { 
            currentPlayer: this.getCurrentPlayer(),
            currentPlayerIndex: this.currentPlayerIndex 
        });
    }

    /**
     * 玩家叫地主
     * @param {string} playerId 玩家ID
     * @param {boolean} call 是否叫地主
     */
    callLandlord(playerId, call) {
        if (this.state !== DouDiZhuGame.STATES.CALLING) {
            throw new Error('当前不是叫地主阶段');
        }

        const player = this.getPlayerById(playerId);
        const currentPlayer = this.getCurrentPlayer();
        
        if (player !== currentPlayer) {
            throw new Error('不是当前玩家的回合');
        }

        player.callLandlord(call);
        
        if (call) {
            // 有人叫地主，确定地主
            this.landlordIndex = this.currentPlayerIndex;
            this.finalizeLandlord();
        } else {
            // 不叫，轮到下一个玩家
            this.nextPlayer();
            
            // 检查是否所有人都不叫
            if (this.checkAllPlayersPassed()) {
                // 重新发牌
                this.startGame();
                return;
            }
        }

        this.emit('landlordCalled', {
            player: player,
            called: call,
            landlordIndex: this.landlordIndex
        });
    }

    /**
     * 确定地主
     */
    finalizeLandlord() {
        const landlord = this.players[this.landlordIndex];
        landlord.setRole(Player.ROLES.LANDLORD);
        landlord.addCards(this.landlordCards);

        // 其他玩家设为农民
        this.players.forEach((player, index) => {
            if (index !== this.landlordIndex) {
                player.setRole(Player.ROLES.FARMER);
            }
        });

        this.currentPlayerIndex = this.landlordIndex;
        this.state = DouDiZhuGame.STATES.PLAYING;

        this.emit('landlordFinalized', {
            landlord: landlord,
            landlordCards: this.landlordCards
        });
    }

    /**
     * 检查是否所有玩家都不叫地主
     */
    checkAllPlayersPassed() {
        let passCount = 0;
        for (const player of this.players) {
            if (!player.hasCalledLandlord) {
                passCount++;
            }
        }
        return passCount === this.players.length;
    }

    /**
     * 玩家出牌
     * @param {string} playerId 玩家ID
     * @param {Card[]} cards 出的牌
     */
    playCards(playerId, cards) {
        if (this.state !== DouDiZhuGame.STATES.PLAYING) {
            throw new Error('当前不是出牌阶段');
        }

        const player = this.getPlayerById(playerId);
        const currentPlayer = this.getCurrentPlayer();
        
        if (player !== currentPlayer) {
            throw new Error('不是当前玩家的回合');
        }

        // 验证出牌是否有效
        if (!this.validatePlay(player, cards)) {
            throw new Error('无效的出牌');
        }

        // 识别牌型
        const pattern = CardPattern.recognize(cards);
        if (!pattern) {
            throw new Error('无法识别的牌型');
        }

        // 检查是否能压过上一手牌
        if (this.lastPlayedPattern && CardPattern.compare(pattern, this.lastPlayedPattern) <= 0) {
            throw new Error('无法压过上一手牌');
        }

        // 执行出牌
        player.removeCards(cards);
        this.lastPlayedPattern = pattern;
        this.lastPlayerIndex = this.currentPlayerIndex;
        this.passCount = 0;

        // 更新游戏倍数
        this.updateGameMultiplier(pattern);

        // 记录回合历史
        this.turnHistory.push({
            playerIndex: this.currentPlayerIndex,
            player: player,
            cards: cards,
            pattern: pattern,
            timestamp: Date.now()
        });

        this.emit('cardsPlayed', {
            player: player,
            cards: cards,
            pattern: pattern,
            remainingCards: player.getHandCount()
        });

        // 检查游戏是否结束
        if (this.checkGameEnd()) {
            this.endGame();
            return;
        }

        // 轮到下一个玩家
        this.nextPlayer();
    }

    /**
     * 玩家不出牌（过）
     * @param {string} playerId 玩家ID
     */
    pass(playerId) {
        if (this.state !== DouDiZhuGame.STATES.PLAYING) {
            throw new Error('当前不是出牌阶段');
        }

        const player = this.getPlayerById(playerId);
        const currentPlayer = this.getCurrentPlayer();
        
        if (player !== currentPlayer) {
            throw new Error('不是当前玩家的回合');
        }

        // 如果是第一手牌，不能过
        if (!this.lastPlayedPattern) {
            throw new Error('第一手牌不能过');
        }

        this.passCount++;
        
        this.emit('playerPassed', {
            player: player,
            passCount: this.passCount
        });

        // 如果连续两个玩家都过，清空上一手牌
        if (this.passCount >= 2) {
            this.lastPlayedPattern = null;
            this.lastPlayerIndex = -1;
            this.passCount = 0;
        }

        // 轮到下一个玩家
        this.nextPlayer();
    }

    /**
     * 验证出牌是否有效
     * @param {Player} player 玩家
     * @param {Card[]} cards 要出的牌
     * @returns {boolean} 是否有效
     */
    validatePlay(player, cards) {
        if (!cards || cards.length === 0) {
            return false;
        }

        // 检查玩家是否拥有这些牌
        if (!player.hasCards(cards)) {
            return false;
        }

        return true;
    }

    /**
     * 更新游戏倍数
     * @param {Object} pattern 牌型
     */
    updateGameMultiplier(pattern) {
        if (pattern.type === CardPattern.TYPES.BOMB) {
            this.bombCount++;
            this.gameScore *= DouDiZhuGame.CONFIG.BOMB_MULTIPLIER;
        } else if (pattern.type === CardPattern.TYPES.ROCKET) {
            this.rocketCount++;
            this.gameScore *= DouDiZhuGame.CONFIG.ROCKET_MULTIPLIER;
        }
    }

    /**
     * 检查游戏是否结束
     * @returns {boolean} 游戏是否结束
     */
    checkGameEnd() {
        return this.players.some(player => player.getHandCount() === 0);
    }

    /**
     * 结束游戏
     */
    endGame() {
        this.state = DouDiZhuGame.STATES.FINISHED;
        
        const winner = this.players.find(player => player.getHandCount() === 0);
        const landlord = this.players[this.landlordIndex];
        const landlordWins = winner === landlord;

        // 计算分数
        this.calculateScores(landlordWins);

        // 更新统计信息
        this.players.forEach(player => {
            player.updateStatistics('gameStart');
            if ((landlordWins && player.isLandlord()) || (!landlordWins && player.isFarmer())) {
                player.updateStatistics('gameWin');
            }
        });

        this.emit('gameEnded', {
            winner: winner,
            landlordWins: landlordWins,
            finalScores: this.players.map(p => ({ id: p.id, score: p.score })),
            gameScore: this.gameScore,
            bombCount: this.bombCount,
            rocketCount: this.rocketCount
        });
    }

    /**
     * 计算分数
     * @param {boolean} landlordWins 地主是否获胜
     */
    calculateScores(landlordWins) {
        const landlord = this.players[this.landlordIndex];
        const farmers = this.players.filter((_, index) => index !== this.landlordIndex);

        if (landlordWins) {
            // 地主获胜
            landlord.addScore(this.gameScore * 2);
            farmers.forEach(farmer => farmer.addScore(-this.gameScore));
        } else {
            // 农民获胜
            landlord.addScore(-this.gameScore * 2);
            farmers.forEach(farmer => farmer.addScore(this.gameScore));
        }
    }

    /**
     * 轮到下一个玩家
     */
    nextPlayer() {
        this.currentPlayerIndex = (this.currentPlayerIndex + 1) % this.players.length;
        
        this.emit('nextPlayer', {
            currentPlayer: this.getCurrentPlayer(),
            currentPlayerIndex: this.currentPlayerIndex
        });
    }

    /**
     * 获取当前玩家
     * @returns {Player} 当前玩家
     */
    getCurrentPlayer() {
        return this.players[this.currentPlayerIndex];
    }

    /**
     * 根据ID获取玩家
     * @param {string} playerId 玩家ID
     * @returns {Player} 玩家对象
     */
    getPlayerById(playerId) {
        return this.players.find(player => player.id === playerId);
    }

    /**
     * 获取地主玩家
     * @returns {Player|null} 地主玩家
     */
    getLandlord() {
        return this.landlordIndex >= 0 ? this.players[this.landlordIndex] : null;
    }

    /**
     * 获取游戏状态
     * @returns {Object} 游戏状态对象
     */
    getGameState() {
        return {
            state: this.state,
            players: this.players.map(p => ({
                id: p.id,
                name: p.name,
                type: p.type,
                role: p.role,
                handCount: p.getHandCount(),
                score: p.score,
                isActive: p.isActive
            })),
            currentPlayerIndex: this.currentPlayerIndex,
            landlordIndex: this.landlordIndex,
            lastPlayedPattern: this.lastPlayedPattern,
            gameScore: this.gameScore,
            bombCount: this.bombCount,
            rocketCount: this.rocketCount
        };
    }

    /**
     * 转换为JSON对象
     * @returns {Object} JSON对象
     */
    toJSON() {
        return {
            state: this.state,
            players: this.players.map(p => p.toJSON()),
            landlordCards: this.landlordCards.map(c => c.toJSON()),
            currentPlayerIndex: this.currentPlayerIndex,
            landlordIndex: this.landlordIndex,
            lastPlayedPattern: this.lastPlayedPattern,
            lastPlayerIndex: this.lastPlayerIndex,
            passCount: this.passCount,
            gameScore: this.gameScore,
            bombCount: this.bombCount,
            rocketCount: this.rocketCount,
            turnHistory: this.turnHistory
        };
    }
}
