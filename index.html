<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTML5斗地主游戏</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/animations.css">
</head>
<body>
    <div id="gameContainer">
        <!-- 游戏标题 -->
        <div id="gameTitle">
            <h1>斗地主</h1>
        </div>
        
        <!-- 游戏主界面 -->
        <div id="gameBoard">
            <canvas id="gameCanvas" width="1200" height="800"></canvas>
        </div>
        
        <!-- 游戏控制面板 -->
        <div id="controlPanel">
            <div id="gameButtons">
                <button id="startBtn" class="game-btn">开始游戏</button>
                <button id="callLandlordBtn" class="game-btn" style="display:none;">叫地主</button>
                <button id="passBtn" class="game-btn" style="display:none;">不叫</button>
                <button id="playCardsBtn" class="game-btn" style="display:none;">出牌</button>
                <button id="skipBtn" class="game-btn" style="display:none;">不出</button>
                <button id="hintBtn" class="game-btn" style="display:none;">提示</button>
                <button id="restartBtn" class="game-btn" style="display:none;">重新开始</button>
            </div>
            
            <!-- 游戏信息显示 -->
            <div id="gameInfo">
                <div id="scoreBoard">
                    <div class="player-score">
                        <span>玩家: </span>
                        <span id="playerScore">0</span>
                    </div>
                    <div class="player-score">
                        <span>电脑1: </span>
                        <span id="ai1Score">0</span>
                    </div>
                    <div class="player-score">
                        <span>电脑2: </span>
                        <span id="ai2Score">0</span>
                    </div>
                </div>
                
                <div id="gameStatus">
                    <p id="statusText">点击开始游戏</p>
                    <p id="currentPlayer"></p>
                </div>
            </div>
        </div>
        
        <!-- 设置面板 -->
        <div id="settingsPanel" style="display:none;">
            <h3>游戏设置</h3>
            <div class="setting-item">
                <label>音效: </label>
                <input type="checkbox" id="soundToggle" checked>
            </div>
            <div class="setting-item">
                <label>AI难度: </label>
                <select id="aiDifficulty">
                    <option value="easy">简单</option>
                    <option value="medium" selected>中等</option>
                    <option value="hard">困难</option>
                </select>
            </div>
            <button id="settingsBtn" class="game-btn">设置</button>
            <button id="closeSettingsBtn" class="game-btn">关闭</button>
        </div>
    </div>
    
    <!-- 加载脚本 -->
    <script type="module" src="js/main.js"></script>
</body>
</html>
