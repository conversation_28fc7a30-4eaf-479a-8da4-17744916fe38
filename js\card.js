/**
 * 卡牌类 - 表示单张卡牌
 */
class Card {
    // 花色常量
    static SUITS = {
        SPADES: 'spades',     // 黑桃
        HEARTS: 'hearts',     // 红桃
        DIAMONDS: 'diamonds', // 方块
        CLUBS: 'clubs'        // 梅花
    };

    // 特殊牌常量
    static SPECIAL = {
        JOKER_SMALL: 'joker_small', // 小王
        JOKER_BIG: 'joker_big'      // 大王
    };

    // 牌面值常量
    static VALUES = {
        3: 3, 4: 4, 5: 5, 6: 6, 7: 7, 8: 8, 9: 9, 10: 10,
        J: 11, Q: 12, K: 13, A: 14, 2: 15,
        JOKER_SMALL: 16, JOKER_BIG: 17
    };

    // 牌面显示名称
    static DISPLAY_NAMES = {
        3: '3', 4: '4', 5: '5', 6: '6', 7: '7', 8: '8', 9: '9', 10: '10',
        J: 'J', Q: 'Q', K: 'K', A: 'A', 2: '2',
        <PERSON><PERSON><PERSON>_SMALL: '小王', JOKER_BIG: '大王'
    };

    // 花色显示符号
    static SUIT_SYMBOLS = {
        spades: '♠',
        hearts: '♥',
        diamonds: '♦',
        clubs: '♣'
    };

    /**
     * 构造函数
     * @param {string|number} value 牌面值
     * @param {string} suit 花色
     */
    constructor(value, suit = null) {
        this.value = value;
        this.suit = suit;
        this.id = this.generateId();
        this.selected = false;
        this.position = { x: 0, y: 0 };
        this.rotation = 0;
        this.scale = 1;
        this.visible = true;
    }

    /**
     * 生成卡牌唯一ID
     * @returns {string} 卡牌ID
     */
    generateId() {
        if (this.value === 'JOKER_SMALL') return 'joker_small';
        if (this.value === 'JOKER_BIG') return 'joker_big';
        return `${this.suit}_${this.value}`;
    }

    /**
     * 获取卡牌的数值权重（用于比较大小）
     * @returns {number} 权重值
     */
    getWeight() {
        return Card.VALUES[this.value] || 0;
    }

    /**
     * 获取卡牌显示名称
     * @returns {string} 显示名称
     */
    getDisplayName() {
        return Card.DISPLAY_NAMES[this.value] || this.value.toString();
    }

    /**
     * 获取花色符号
     * @returns {string} 花色符号
     */
    getSuitSymbol() {
        return Card.SUIT_SYMBOLS[this.suit] || '';
    }

    /**
     * 获取卡牌完整显示文本
     * @returns {string} 完整显示文本
     */
    getFullDisplayText() {
        if (this.isJoker()) {
            return this.getDisplayName();
        }
        return `${this.getSuitSymbol()}${this.getDisplayName()}`;
    }

    /**
     * 判断是否为王牌
     * @returns {boolean} 是否为王牌
     */
    isJoker() {
        return this.value === 'JOKER_SMALL' || this.value === 'JOKER_BIG';
    }

    /**
     * 判断是否为红色牌
     * @returns {boolean} 是否为红色牌
     */
    isRed() {
        return this.suit === Card.SUITS.HEARTS || 
               this.suit === Card.SUITS.DIAMONDS ||
               this.value === 'JOKER_SMALL';
    }

    /**
     * 判断是否为黑色牌
     * @returns {boolean} 是否为黑色牌
     */
    isBlack() {
        return this.suit === Card.SUITS.SPADES || 
               this.suit === Card.SUITS.CLUBS ||
               this.value === 'JOKER_BIG';
    }

    /**
     * 比较两张卡牌的大小
     * @param {Card} other 另一张卡牌
     * @returns {number} 比较结果 (-1: 小于, 0: 等于, 1: 大于)
     */
    compareTo(other) {
        const thisWeight = this.getWeight();
        const otherWeight = other.getWeight();
        
        if (thisWeight < otherWeight) return -1;
        if (thisWeight > otherWeight) return 1;
        return 0;
    }

    /**
     * 判断两张卡牌是否相等
     * @param {Card} other 另一张卡牌
     * @returns {boolean} 是否相等
     */
    equals(other) {
        return this.value === other.value && this.suit === other.suit;
    }

    /**
     * 克隆卡牌
     * @returns {Card} 克隆的卡牌
     */
    clone() {
        const cloned = new Card(this.value, this.suit);
        cloned.selected = this.selected;
        cloned.position = { ...this.position };
        cloned.rotation = this.rotation;
        cloned.scale = this.scale;
        cloned.visible = this.visible;
        return cloned;
    }

    /**
     * 设置卡牌位置
     * @param {number} x X坐标
     * @param {number} y Y坐标
     */
    setPosition(x, y) {
        this.position.x = x;
        this.position.y = y;
    }

    /**
     * 设置卡牌旋转角度
     * @param {number} rotation 旋转角度（度）
     */
    setRotation(rotation) {
        this.rotation = rotation;
    }

    /**
     * 设置卡牌缩放
     * @param {number} scale 缩放比例
     */
    setScale(scale) {
        this.scale = scale;
    }

    /**
     * 设置卡牌可见性
     * @param {boolean} visible 是否可见
     */
    setVisible(visible) {
        this.visible = visible;
    }

    /**
     * 选中/取消选中卡牌
     * @param {boolean} selected 是否选中
     */
    setSelected(selected) {
        this.selected = selected;
    }

    /**
     * 切换选中状态
     */
    toggleSelected() {
        this.selected = !this.selected;
    }

    /**
     * 转换为JSON对象
     * @returns {Object} JSON对象
     */
    toJSON() {
        return {
            value: this.value,
            suit: this.suit,
            id: this.id,
            selected: this.selected,
            position: this.position,
            rotation: this.rotation,
            scale: this.scale,
            visible: this.visible
        };
    }

    /**
     * 从JSON对象创建卡牌
     * @param {Object} json JSON对象
     * @returns {Card} 卡牌实例
     */
    static fromJSON(json) {
        const card = new Card(json.value, json.suit);
        card.selected = json.selected || false;
        card.position = json.position || { x: 0, y: 0 };
        card.rotation = json.rotation || 0;
        card.scale = json.scale || 1;
        card.visible = json.visible !== undefined ? json.visible : true;
        return card;
    }

    /**
     * 获取卡牌的CSS类名
     * @returns {string} CSS类名
     */
    getCSSClass() {
        let className = 'card';
        if (this.selected) className += ' selected';
        if (this.isRed()) className += ' red';
        if (this.isBlack()) className += ' black';
        if (this.isJoker()) className += ' joker';
        return className;
    }

    /**
     * 转换为字符串表示
     * @returns {string} 字符串表示
     */
    toString() {
        return this.getFullDisplayText();
    }
}

// 导出卡牌类
export default Card;
