import Utils from './utils.js';

/**
 * 游戏音效管理器
 */
export default class AudioManager extends Utils.EventEmitter {
    /**
     * 构造函数
     */
    constructor() {
        super();
        this.audioContext = null;
        this.sounds = new Map();
        this.musicVolume = 0.7;
        this.soundVolume = 0.8;
        this.enabled = true;
        this.musicEnabled = true;
        this.soundEnabled = true;
        
        this.initializeAudioContext();
        this.loadSounds();
    }

    /**
     * 初始化音频上下文
     */
    initializeAudioContext() {
        try {
            // 创建音频上下文
            const AudioContext = window.AudioContext || window.webkitAudioContext;
            this.audioContext = new AudioContext();
            
            // 创建主音量控制节点
            this.masterGain = this.audioContext.createGain();
            this.masterGain.connect(this.audioContext.destination);
            
            // 创建音效和音乐的分离控制节点
            this.soundGain = this.audioContext.createGain();
            this.musicGain = this.audioContext.createGain();
            
            this.soundGain.connect(this.masterGain);
            this.musicGain.connect(this.masterGain);
            
            this.updateVolume();
            
        } catch (error) {
            console.warn('音频上下文初始化失败:', error);
            this.audioContext = null;
        }
    }

    /**
     * 加载音效文件
     */
    loadSounds() {
        // 定义音效文件映射
        const soundFiles = {
            // 游戏音效
            'deal': this.generateDealSound(),
            'play': this.generatePlaySound(),
            'select': this.generateSelectSound(),
            'button': this.generateButtonSound(),
            'win': this.generateWinSound(),
            'lose': this.generateLoseSound(),
            'bomb': this.generateBombSound(),
            'rocket': this.generateRocketSound(),
            'landlord': this.generateLandlordSound(),
            'pass': this.generatePassSound(),
            
            // 背景音乐
            'bgm': this.generateBackgroundMusic()
        };

        // 加载所有音效
        Object.entries(soundFiles).forEach(([name, audioBuffer]) => {
            this.sounds.set(name, audioBuffer);
        });
    }

    /**
     * 生成发牌音效
     */
    generateDealSound() {
        if (!this.audioContext) return null;
        
        const duration = 0.1;
        const sampleRate = this.audioContext.sampleRate;
        const buffer = this.audioContext.createBuffer(1, duration * sampleRate, sampleRate);
        const data = buffer.getChannelData(0);
        
        for (let i = 0; i < data.length; i++) {
            const t = i / sampleRate;
            // 生成短促的噪音效果
            data[i] = (Math.random() * 2 - 1) * Math.exp(-t * 20) * 0.3;
        }
        
        return buffer;
    }

    /**
     * 生成出牌音效
     */
    generatePlaySound() {
        if (!this.audioContext) return null;
        
        const duration = 0.2;
        const sampleRate = this.audioContext.sampleRate;
        const buffer = this.audioContext.createBuffer(1, duration * sampleRate, sampleRate);
        const data = buffer.getChannelData(0);
        
        for (let i = 0; i < data.length; i++) {
            const t = i / sampleRate;
            // 生成清脆的点击声
            data[i] = Math.sin(2 * Math.PI * 800 * t) * Math.exp(-t * 10) * 0.4;
        }
        
        return buffer;
    }

    /**
     * 生成选择音效
     */
    generateSelectSound() {
        if (!this.audioContext) return null;
        
        const duration = 0.1;
        const sampleRate = this.audioContext.sampleRate;
        const buffer = this.audioContext.createBuffer(1, duration * sampleRate, sampleRate);
        const data = buffer.getChannelData(0);
        
        for (let i = 0; i < data.length; i++) {
            const t = i / sampleRate;
            // 生成轻柔的提示音
            data[i] = Math.sin(2 * Math.PI * 1200 * t) * Math.exp(-t * 15) * 0.2;
        }
        
        return buffer;
    }

    /**
     * 生成按钮音效
     */
    generateButtonSound() {
        if (!this.audioContext) return null;
        
        const duration = 0.15;
        const sampleRate = this.audioContext.sampleRate;
        const buffer = this.audioContext.createBuffer(1, duration * sampleRate, sampleRate);
        const data = buffer.getChannelData(0);
        
        for (let i = 0; i < data.length; i++) {
            const t = i / sampleRate;
            // 生成按钮点击声
            data[i] = Math.sin(2 * Math.PI * 600 * t) * Math.exp(-t * 8) * 0.3;
        }
        
        return buffer;
    }

    /**
     * 生成胜利音效
     */
    generateWinSound() {
        if (!this.audioContext) return null;
        
        const duration = 1.0;
        const sampleRate = this.audioContext.sampleRate;
        const buffer = this.audioContext.createBuffer(1, duration * sampleRate, sampleRate);
        const data = buffer.getChannelData(0);
        
        for (let i = 0; i < data.length; i++) {
            const t = i / sampleRate;
            // 生成上升的胜利音效
            const freq = 440 + t * 220;
            data[i] = Math.sin(2 * Math.PI * freq * t) * Math.exp(-t * 2) * 0.5;
        }
        
        return buffer;
    }

    /**
     * 生成失败音效
     */
    generateLoseSound() {
        if (!this.audioContext) return null;
        
        const duration = 0.8;
        const sampleRate = this.audioContext.sampleRate;
        const buffer = this.audioContext.createBuffer(1, duration * sampleRate, sampleRate);
        const data = buffer.getChannelData(0);
        
        for (let i = 0; i < data.length; i++) {
            const t = i / sampleRate;
            // 生成下降的失败音效
            const freq = 440 - t * 200;
            data[i] = Math.sin(2 * Math.PI * freq * t) * Math.exp(-t * 3) * 0.4;
        }
        
        return buffer;
    }

    /**
     * 生成炸弹音效
     */
    generateBombSound() {
        if (!this.audioContext) return null;
        
        const duration = 0.5;
        const sampleRate = this.audioContext.sampleRate;
        const buffer = this.audioContext.createBuffer(1, duration * sampleRate, sampleRate);
        const data = buffer.getChannelData(0);
        
        for (let i = 0; i < data.length; i++) {
            const t = i / sampleRate;
            // 生成爆炸音效
            data[i] = (Math.random() * 2 - 1) * Math.exp(-t * 5) * 0.6;
        }
        
        return buffer;
    }

    /**
     * 生成火箭音效
     */
    generateRocketSound() {
        if (!this.audioContext) return null;
        
        const duration = 0.7;
        const sampleRate = this.audioContext.sampleRate;
        const buffer = this.audioContext.createBuffer(1, duration * sampleRate, sampleRate);
        const data = buffer.getChannelData(0);
        
        for (let i = 0; i < data.length; i++) {
            const t = i / sampleRate;
            // 生成火箭发射音效
            const freq = 200 + t * 800;
            data[i] = Math.sin(2 * Math.PI * freq * t) * Math.exp(-t * 2) * 0.7;
        }
        
        return buffer;
    }

    /**
     * 生成地主音效
     */
    generateLandlordSound() {
        if (!this.audioContext) return null;
        
        const duration = 0.3;
        const sampleRate = this.audioContext.sampleRate;
        const buffer = this.audioContext.createBuffer(1, duration * sampleRate, sampleRate);
        const data = buffer.getChannelData(0);
        
        for (let i = 0; i < data.length; i++) {
            const t = i / sampleRate;
            // 生成庄重的地主音效
            data[i] = Math.sin(2 * Math.PI * 330 * t) * Math.exp(-t * 5) * 0.5;
        }
        
        return buffer;
    }

    /**
     * 生成过牌音效
     */
    generatePassSound() {
        if (!this.audioContext) return null;
        
        const duration = 0.2;
        const sampleRate = this.audioContext.sampleRate;
        const buffer = this.audioContext.createBuffer(1, duration * sampleRate, sampleRate);
        const data = buffer.getChannelData(0);
        
        for (let i = 0; i < data.length; i++) {
            const t = i / sampleRate;
            // 生成轻柔的过牌音效
            data[i] = Math.sin(2 * Math.PI * 400 * t) * Math.exp(-t * 12) * 0.3;
        }
        
        return buffer;
    }

    /**
     * 生成背景音乐
     */
    generateBackgroundMusic() {
        if (!this.audioContext) return null;
        
        const duration = 10.0; // 10秒循环
        const sampleRate = this.audioContext.sampleRate;
        const buffer = this.audioContext.createBuffer(2, duration * sampleRate, sampleRate);
        
        // 生成简单的背景音乐
        for (let channel = 0; channel < 2; channel++) {
            const data = buffer.getChannelData(channel);
            
            for (let i = 0; i < data.length; i++) {
                const t = i / sampleRate;
                // 生成和谐的背景音乐
                const melody = Math.sin(2 * Math.PI * 220 * t) * 0.1 +
                              Math.sin(2 * Math.PI * 330 * t) * 0.08 +
                              Math.sin(2 * Math.PI * 440 * t) * 0.06;
                
                data[i] = melody * (0.5 + 0.5 * Math.sin(2 * Math.PI * 0.1 * t));
            }
        }
        
        return buffer;
    }

    /**
     * 播放音效
     * @param {string} soundName 音效名称
     * @param {Object} options 播放选项
     */
    playSound(soundName, options = {}) {
        if (!this.enabled || !this.soundEnabled || !this.audioContext) return;
        
        const buffer = this.sounds.get(soundName);
        if (!buffer) {
            console.warn(`音效 "${soundName}" 未找到`);
            return;
        }

        try {
            const source = this.audioContext.createBufferSource();
            source.buffer = buffer;
            
            // 连接到音效增益节点
            source.connect(this.soundGain);
            
            // 设置播放参数
            const volume = options.volume || 1.0;
            const pitch = options.pitch || 1.0;
            
            source.playbackRate.value = pitch;
            
            // 开始播放
            source.start(0);
            
            this.emit('soundPlayed', { soundName, options });
            
        } catch (error) {
            console.warn(`播放音效 "${soundName}" 失败:`, error);
        }
    }

    /**
     * 播放背景音乐
     * @param {boolean} loop 是否循环播放
     */
    playBackgroundMusic(loop = true) {
        if (!this.enabled || !this.musicEnabled || !this.audioContext) return;
        
        this.stopBackgroundMusic();
        
        const buffer = this.sounds.get('bgm');
        if (!buffer) return;

        try {
            this.bgmSource = this.audioContext.createBufferSource();
            this.bgmSource.buffer = buffer;
            this.bgmSource.loop = loop;
            
            // 连接到音乐增益节点
            this.bgmSource.connect(this.musicGain);
            
            // 开始播放
            this.bgmSource.start(0);
            
            this.emit('musicStarted');
            
        } catch (error) {
            console.warn('播放背景音乐失败:', error);
        }
    }

    /**
     * 停止背景音乐
     */
    stopBackgroundMusic() {
        if (this.bgmSource) {
            try {
                this.bgmSource.stop();
                this.bgmSource = null;
                this.emit('musicStopped');
            } catch (error) {
                console.warn('停止背景音乐失败:', error);
            }
        }
    }

    /**
     * 设置音效音量
     * @param {number} volume 音量 (0-1)
     */
    setSoundVolume(volume) {
        this.soundVolume = Math.max(0, Math.min(1, volume));
        this.updateVolume();
    }

    /**
     * 设置音乐音量
     * @param {number} volume 音量 (0-1)
     */
    setMusicVolume(volume) {
        this.musicVolume = Math.max(0, Math.min(1, volume));
        this.updateVolume();
    }

    /**
     * 更新音量设置
     */
    updateVolume() {
        if (!this.audioContext) return;
        
        if (this.soundGain) {
            this.soundGain.gain.value = this.soundEnabled ? this.soundVolume : 0;
        }
        
        if (this.musicGain) {
            this.musicGain.gain.value = this.musicEnabled ? this.musicVolume : 0;
        }
    }

    /**
     * 启用/禁用音效
     * @param {boolean} enabled 是否启用
     */
    setSoundEnabled(enabled) {
        this.soundEnabled = enabled;
        this.updateVolume();
    }

    /**
     * 启用/禁用音乐
     * @param {boolean} enabled 是否启用
     */
    setMusicEnabled(enabled) {
        this.musicEnabled = enabled;
        this.updateVolume();
        
        if (!enabled) {
            this.stopBackgroundMusic();
        } else {
            this.playBackgroundMusic();
        }
    }

    /**
     * 启用/禁用所有音频
     * @param {boolean} enabled 是否启用
     */
    setEnabled(enabled) {
        this.enabled = enabled;
        
        if (!enabled) {
            this.stopBackgroundMusic();
        }
        
        this.updateVolume();
    }

    /**
     * 恢复音频上下文（用户交互后）
     */
    resumeAudioContext() {
        if (this.audioContext && this.audioContext.state === 'suspended') {
            this.audioContext.resume().then(() => {
                console.log('音频上下文已恢复');
            });
        }
    }

    /**
     * 获取音频设置
     * @returns {Object} 音频设置对象
     */
    getSettings() {
        return {
            enabled: this.enabled,
            soundEnabled: this.soundEnabled,
            musicEnabled: this.musicEnabled,
            soundVolume: this.soundVolume,
            musicVolume: this.musicVolume
        };
    }

    /**
     * 应用音频设置
     * @param {Object} settings 音频设置对象
     */
    applySettings(settings) {
        if (settings.enabled !== undefined) this.setEnabled(settings.enabled);
        if (settings.soundEnabled !== undefined) this.setSoundEnabled(settings.soundEnabled);
        if (settings.musicEnabled !== undefined) this.setMusicEnabled(settings.musicEnabled);
        if (settings.soundVolume !== undefined) this.setSoundVolume(settings.soundVolume);
        if (settings.musicVolume !== undefined) this.setMusicVolume(settings.musicVolume);
    }
}
